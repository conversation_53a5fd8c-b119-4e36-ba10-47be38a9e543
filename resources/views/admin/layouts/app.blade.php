<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Admin Dashboard') - {{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Alpine.js Debug Script -->
    <script>
        document.addEventListener('alpine:init', () => {
            console.log('Alpine.js initialized successfully');
        });
        
        // Fallback dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Add click event listener for dropdown toggle
            const dropdownButton = document.querySelector('[data-dropdown-toggle]');
            const dropdownMenu = document.querySelector('#user-dropdown');
            
            if (dropdownButton && dropdownMenu) {
                dropdownButton.addEventListener('click', function(e) {
                    e.stopPropagation();
                    dropdownMenu.style.display = dropdownMenu.style.display === 'none' ? 'block' : 'none';
                });
                
                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!dropdownButton.contains(e.target) && !dropdownMenu.contains(e.target)) {
                        dropdownMenu.style.display = 'none';
                    }
                });
            }
        });
    </script>
    
    <!-- Additional CSS for Admin Dashboard -->
    <style>
        /* Reset and base styles */
        * {
            box-sizing: border-box;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.5;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        
        /* Layout utilities */
        .flex { display: flex !important; }
        .items-center { align-items: center !important; }
        .justify-between { justify-content: space-between !important; }
        .justify-center { justify-content: center !important; }
        .space-x-2 > * + * { margin-left: 0.5rem !important; }
        .space-x-3 > * + * { margin-left: 0.75rem !important; }
        .space-x-4 > * + * { margin-left: 1rem !important; }
        .space-y-2 > * + * { margin-top: 0.5rem !important; }
        .space-y-4 > * + * { margin-top: 1rem !important; }
        .space-y-6 > * + * { margin-top: 1.5rem !important; }
        .space-y-8 > * + * { margin-top: 2rem !important; }
        
        /* Grid system */
        .grid { display: grid !important; }
        .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
        .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
        .grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
        .gap-6 { gap: 1.5rem !important; }
        .gap-8 { gap: 2rem !important; }
        
        /* Spacing */
        .p-2 { padding: 0.5rem !important; }
        .p-3 { padding: 0.75rem !important; }
        .p-4 { padding: 1rem !important; }
        .p-6 { padding: 1.5rem !important; }
        .p-8 { padding: 2rem !important; }
        .px-4 { padding-left: 1rem !important; padding-right: 1rem !important; }
        .px-6 { padding-left: 1.5rem !important; padding-right: 1.5rem !important; }
        .py-3 { padding-top: 0.75rem !important; padding-bottom: 0.75rem !important; }
        .py-4 { padding-top: 1rem !important; padding-bottom: 1rem !important; }
        .py-8 { padding-top: 2rem !important; padding-bottom: 2rem !important; }
        .pb-24 { padding-bottom: 6rem !important; }
        .mb-2 { margin-bottom: 0.5rem !important; }
        .mb-3 { margin-bottom: 0.75rem !important; }
        .mb-4 { margin-bottom: 1rem !important; }
        .mb-6 { margin-bottom: 1.5rem !important; }
        .mb-8 { margin-bottom: 2rem !important; }
        .mr-3 { margin-right: 0.75rem !important; }
        .mr-4 { margin-right: 1rem !important; }
        .ml-4 { margin-left: 1rem !important; }
        .mt-1 { margin-top: 0.25rem !important; }
        .mt-8 { margin-top: 2rem !important; }
        
        /* Sizing */
        .w-2 { width: 0.5rem !important; }
        .w-4 { width: 1rem !important; }
        .w-5 { width: 1.25rem !important; }
        .w-6 { width: 1.5rem !important; }
        .w-8 { width: 2rem !important; }
        .w-10 { width: 2.5rem !important; }
        .w-24 { width: 6rem !important; }
        .w-32 { width: 8rem !important; }
        .w-48 { width: 12rem !important; }
        .w-64 { width: 16rem !important; }
        .h-2 { height: 0.5rem !important; }
        .h-3 { height: 0.75rem !important; }
        .h-4 { height: 1rem !important; }
        .h-5 { height: 1.25rem !important; }
        .h-6 { height: 1.5rem !important; }
        .h-8 { height: 2rem !important; }
        .h-10 { height: 2.5rem !important; }
        .h-20 { height: 5rem !important; }
        .h-screen { height: 100vh !important; }
        
        /* Border radius */
        .rounded-lg { border-radius: 0.5rem !important; }
        .rounded-xl { border-radius: 0.75rem !important; }
        .rounded-2xl { border-radius: 1rem !important; }
        .rounded-full { border-radius: 9999px !important; }
        
        /* Typography */
        .text-xs { font-size: 0.75rem !important; line-height: 1rem !important; }
        .text-sm { font-size: 0.875rem !important; line-height: 1.25rem !important; }
        .text-lg { font-size: 1.125rem !important; line-height: 1.75rem !important; }
        .text-xl { font-size: 1.25rem !important; line-height: 1.75rem !important; }
        .text-2xl { font-size: 1.5rem !important; line-height: 2rem !important; }
        .text-3xl { font-size: 1.875rem !important; line-height: 2.25rem !important; }
        .text-4xl { font-size: 2.25rem !important; line-height: 2.5rem !important; }
        .font-medium { font-weight: 500 !important; }
        .font-semibold { font-weight: 600 !important; }
        .font-bold { font-weight: 700 !important; }
        .text-white { color: rgb(255 255 255) !important; }
        .text-black { color: rgb(0 0 0) !important; }
        .text-blue-100 { color: rgb(219 234 254) !important; }
        .text-blue-200 { color: rgb(191 219 254) !important; }
        .text-blue-600 { color: rgb(37 99 235) !important; }
        .text-green-600 { color: rgb(22 163 74) !important; }
        .text-green-700 { color: rgb(21 128 61) !important; }
        .text-yellow-700 { color: rgb(161 98 7) !important; }
        .text-red-700 { color: rgb(185 28 28) !important; }
        .text-purple-600 { color: rgb(147 51 234) !important; }
        .text-orange-600 { color: rgb(234 88 12) !important; }
        .text-gray-300 { color: var(--text-tertiary) !important; }
        .text-gray-500 { color: var(--text-secondary) !important; }
        .text-gray-600 { color: var(--text-secondary) !important; }
        .text-gray-700 { color: var(--text-primary) !important; }
        .text-gray-900 { color: var(--text-primary) !important; }
        
        /* Background colors - Dark mode compatible */
        .bg-white { background-color: var(--bg-secondary) !important; }
        .bg-gray-50 { background-color: var(--bg-primary) !important; }
        .bg-gray-100 { background-color: var(--bg-tertiary) !important; }
        .bg-gray-200 { background-color: var(--border-secondary) !important; }
        .bg-blue-50 { background-color: rgb(239 246 255) !important; }
        .bg-blue-100 { background-color: rgb(219 234 254) !important; }
        .bg-blue-200 { background-color: rgb(191 219 254) !important; }
        .bg-blue-500 { background-color: rgb(59 130 246) !important; }
        .bg-green-50 { background-color: rgb(240 253 244) !important; }
        .bg-green-100 { background-color: rgb(220 252 231) !important; }
        .bg-green-200 { background-color: rgb(187 247 208) !important; }
        .bg-green-400 { background-color: rgb(74 222 128) !important; }
        .bg-green-500 { background-color: rgb(34 197 94) !important; }
        .bg-yellow-50 { background-color: rgb(254 252 232) !important; }
        .bg-yellow-100 { background-color: rgb(254 249 195) !important; }
        .bg-yellow-200 { background-color: rgb(254 240 138) !important; }
        .bg-yellow-500 { background-color: rgb(234 179 8) !important; }
        .bg-red-50 { background-color: rgb(254 242 242) !important; }
        .bg-red-100 { background-color: rgb(254 226 226) !important; }
        .bg-red-200 { background-color: rgb(254 202 202) !important; }
        .bg-red-500 { background-color: rgb(239 68 68) !important; }
        .bg-purple-50 { background-color: rgb(250 245 255) !important; }
        .bg-purple-100 { background-color: rgb(243 232 255) !important; }
        .bg-purple-200 { background-color: rgb(233 213 255) !important; }
        .bg-purple-500 { background-color: rgb(168 85 247) !important; }
        .bg-orange-100 { background-color: rgb(255 237 213) !important; }
        .bg-orange-200 { background-color: rgb(254 215 170) !important; }
        
        /* Borders - Dark mode compatible */
        .border { border-width: 1px !important; }
        .border-gray-100 { border-color: var(--border-primary) !important; }
        .border-gray-200 { border-color: var(--border-secondary) !important; }
        .border-blue-100 { border-color: rgb(219 234 254) !important; }
        .border-blue-200 { border-color: rgb(191 219 254) !important; }
        .border-green-100 { border-color: rgb(220 252 231) !important; }
        .border-green-200 { border-color: rgb(187 247 208) !important; }
        .border-yellow-100 { border-color: rgb(254 249 195) !important; }
        .border-red-100 { border-color: rgb(254 226 226) !important; }
        .border-purple-200 { border-color: rgb(233 213 255) !important; }
        
        /* Shadows - Dark mode compatible */
        .shadow-sm { box-shadow: var(--shadow-primary) !important; }
        .shadow-md { box-shadow: var(--shadow-secondary) !important; }
        .shadow-lg { box-shadow: var(--shadow-secondary) !important; }
        .shadow-xl { box-shadow: var(--shadow-large) !important; }
        .shadow-2xl { box-shadow: var(--shadow-large) !important; }
        .shadow-inner { box-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05) !important; }
        
        /* Positioning */
        .relative { position: relative !important; }
        .absolute { position: absolute !important; }
        .fixed { position: fixed !important; }
        .sticky { position: sticky !important; }
        .inset-0 { top: 0px !important; right: 0px !important; bottom: 0px !important; left: 0px !important; }
        .inset-y-0 { top: 0px !important; bottom: 0px !important; }
        .left-0 { left: 0px !important; }
        .right-0 { right: 0px !important; }
        .top-0 { top: 0px !important; }
        .top-1 { top: 0.25rem !important; }
        .right-1 { right: 0.25rem !important; }
        .bottom-0 { bottom: 0px !important; }
        .z-40 { z-index: 40 !important; }
        .z-50 { z-index: 50 !important; }
        
        /* Flexbox */
        .flex-1 { flex: 1 1 0% !important; }
        .flex-col { flex-direction: column !important; }
        .flex-row { flex-direction: row !important; }
        
        /* Overflow */
        .overflow-hidden { overflow: hidden !important; }
        .overflow-y-auto { overflow-y: auto !important; }
        .overflow-x-hidden { overflow-x: hidden !important; }
        
        /* Container */
        .container { 
            width: 100% !important; 
            margin-left: auto !important; 
            margin-right: auto !important; 
            max-width: 1200px !important; 
        }
        .mx-auto { margin-left: auto !important; margin-right: auto !important; }
        
        /* Display */
        .hidden { display: none !important; }
        .block { display: block !important; }
        .visible { visibility: visible !important; }
        .invisible { visibility: hidden !important; }
        .opacity-0 { opacity: 0 !important; }
        .opacity-100 { opacity: 1 !important; }
        .scale-95 { transform: scale(0.95) !important; }
        .scale-100 { transform: scale(1) !important; }
        
        /* Text utilities */
        .min-w-0 { min-width: 0px !important; }
        .truncate { overflow: hidden !important; text-overflow: ellipsis !important; white-space: nowrap !important; }
        .uppercase { text-transform: uppercase !important; }
        .tracking-wider { letter-spacing: 0.05em !important; }
        .tracking-wide { letter-spacing: 0.025em !important; }
        
        /* Transitions */
        .transition-all { 
            transition-property: all !important; 
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important; 
            transition-duration: 150ms !important; 
        }
        .transition-colors { 
            transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important; 
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important; 
            transition-duration: 150ms !important; 
        }
        .transition-transform { 
            transition-property: transform !important; 
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important; 
            transition-duration: 150ms !important; 
        }
        .duration-200 { transition-duration: 200ms !important; }
        .duration-300 { transition-duration: 300ms !important; }
        .duration-1000 { transition-duration: 1000ms !important; }
        
        /* Transform */
        .transform { 
            transform: translate(var(--tw-translate-x), var(--tw-translate-y)) 
                      rotate(var(--tw-rotate)) 
                      skewX(var(--tw-skew-x)) 
                      skewY(var(--tw-skew-y)) 
                      scaleX(var(--tw-scale-x)) 
                      scaleY(var(--tw-scale-y)) !important; 
        }
        .-translate-x-full { --tw-translate-x: -100% !important; }
        .translate-x-0 { --tw-translate-x: 0px !important; }
        
        /* Group hover effects */
        .group:hover .group-hover\\:scale-110 { 
            --tw-scale-x: 1.1 !important; 
            --tw-scale-y: 1.1 !important; 
        }
        .group:hover .group-hover\\:bg-blue-600 { background-color: rgb(37 99 235) !important; }
        .group:hover .group-hover\\:bg-purple-600 { background-color: rgb(147 51 234) !important; }
        .group:hover .group-hover\\:bg-green-600 { background-color: rgb(22 163 74) !important; }
        .group:hover .group-hover\\:text-blue-600 { color: rgb(37 99 235) !important; }
        .group:hover .group-hover\\:text-purple-600 { color: rgb(147 51 234) !important; }
        .group:hover .group-hover\\:text-green-600 { color: rgb(22 163 74) !important; }
        
        /* Hover effects */
        .hover\\:shadow-xl:hover { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1) !important; }
        .hover\\:shadow-md:hover { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important; }
        .hover\\:-translate-y-1:hover { --tw-translate-y: -0.25rem !important; }
        
        /* Gradients - using CSS custom properties for better browser support */
        .bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)) !important; }
        .bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)) !important; }
        .from-blue-600 { --tw-gradient-from: #2563eb !important; --tw-gradient-to: rgb(37 99 235 / 0) !important; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important; }
        .via-purple-600 { --tw-gradient-to: rgb(147 51 234 / 0) !important; --tw-gradient-stops: var(--tw-gradient-from), #9333ea, var(--tw-gradient-to) !important; }
        .to-indigo-700 { --tw-gradient-to: #4338ca !important; }
        .from-blue-100 { --tw-gradient-from: #dbeafe !important; --tw-gradient-to: rgb(219 234 254 / 0) !important; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important; }
        .to-blue-200 { --tw-gradient-to: #bfdbfe !important; }
        .from-green-100 { --tw-gradient-from: #dcfce7 !important; --tw-gradient-to: rgb(220 252 231 / 0) !important; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important; }
        .to-green-200 { --tw-gradient-to: #bbf7d0 !important; }
        .from-purple-100 { --tw-gradient-from: #f3e8ff !important; --tw-gradient-to: rgb(243 232 255 / 0) !important; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important; }
        .to-purple-200 { --tw-gradient-to: #e9d5ff !important; }
        .from-orange-100 { --tw-gradient-from: #fed7aa !important; --tw-gradient-to: rgb(254 215 170 / 0) !important; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important; }
        .to-orange-200 { --tw-gradient-to: #fecaca !important; }
        .from-green-400 { --tw-gradient-from: #4ade80 !important; --tw-gradient-to: rgb(74 222 128 / 0) !important; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important; }
        .to-green-500 { --tw-gradient-to: #22c55e !important; }
        .from-yellow-400 { --tw-gradient-from: #facc15 !important; --tw-gradient-to: rgb(250 204 21 / 0) !important; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important; }
        .to-yellow-500 { --tw-gradient-to: #eab308 !important; }
        .from-red-400 { --tw-gradient-from: #f87171 !important; --tw-gradient-to: rgb(248 113 113 / 0) !important; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important; }
        .to-red-500 { --tw-gradient-to: #ef4444 !important; }
        
        /* Custom styles for better visibility - Dark mode compatible */
        .admin-card {
            background: var(--bg-secondary) !important;
            border: 1px solid var(--border-primary) !important;
            border-radius: 1rem !important;
            padding: 1.5rem !important;
            box-shadow: var(--shadow-secondary) !important;
            transition: all 0.3s ease !important;
        }
        
        .admin-card:hover {
            box-shadow: var(--shadow-large) !important;
            transform: translateY(-0.25rem) !important;
        }
        
        /* Responsive design */
        @media (min-width: 768px) {
            .md\\:block { display: block !important; }
            .md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
            .md\\:flex-row { flex-direction: row !important; }
            .md\\:items-center { align-items: center !important; }
            .md\\:mb-0 { margin-bottom: 0px !important; }
            .md\\:text-4xl { font-size: 2.25rem !important; line-height: 2.5rem !important; }
        }
        
        @media (min-width: 1024px) {
            .lg\\:hidden { display: none !important; }
            .lg\\:ml-0 { margin-left: 0px !important; }
            .lg\\:static { position: static !important; }
            .lg\\:inset-0 { top: 0px !important; right: 0px !important; bottom: 0px !important; left: 0px !important; }
            .lg\\:z-auto { z-index: auto !important; }
            .lg\\:translate-x-0 { --tw-translate-x: 0px !important; }
            .lg\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
            .lg\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
        }
        
        /* Alpine.js cloak */
        [x-cloak] { display: none !important; }
        
        /* Dark Mode Variables */
        :root {
            --bg-primary: #f9fafb;
            --bg-secondary: #ffffff;
            --bg-tertiary: #f3f4f6;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --text-tertiary: #9ca3af;
            --border-primary: #e5e7eb;
            --border-secondary: #d1d5db;
            --shadow-primary: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            --shadow-secondary: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-large: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        [data-theme="dark"] {
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-tertiary: #94a3b8;
            --border-primary: #334155;
            --border-secondary: #475569;
            --shadow-primary: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
            --shadow-secondary: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
            --shadow-large: 0 20px 25px -5px rgba(0, 0, 0, 0.4);
        }
        
        /* Dark mode toggle button */
        .dark-mode-toggle {
            position: relative;
            width: 50px;
            height: 26px;
            background: var(--bg-tertiary);
            border-radius: 13px;
            border: 2px solid var(--border-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }
        
        .dark-mode-toggle::before {
            content: '☀️';
            position: absolute;
            top: 1px;
            left: 2px;
            width: 18px;
            height: 18px;
            background: #fbbf24;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
        }
        
        [data-theme="dark"] .dark-mode-toggle {
            background: #1e293b;
            border-color: #475569;
        }
        
        [data-theme="dark"] .dark-mode-toggle::before {
            content: '🌙';
            transform: translateX(24px);
            background: #e2e8f0;
        }
        
        .dark-mode-toggle:hover {
            box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
        }
        
        /* Dropdown specific styles */
        .dropdown-menu {
            position: absolute !important;
            z-index: 9999 !important;
            pointer-events: auto !important;
            display: none !important;
        }
        
        /* Show dropdown when Alpine.js sets x-show */
        .dropdown-menu[style*="display: block"], 
        .dropdown-menu[x-show="true"] {
            display: block !important;
        }
        
        /* Ensure dropdowns are visible when open */
        [x-show] {
            transition: all 0.3s ease !important;
        }
        
        /* Force visibility for main content */
        main {
            visibility: visible !important;
            opacity: 1 !important;
        }
        
        /* Ensure content is properly displayed */
        .dashboard-content {
            background: transparent !important;
            color: inherit !important;
        }
        
        /* Fix for overlay blur issue */
        .content-area {
            position: relative !important;
            z-index: 10 !important;
            background: var(--bg-primary) !important;
        }
        
        /* Ensure mobile overlay only shows on mobile and doesn't interfere with desktop */
        .mobile-overlay {
            position: fixed !important;
            z-index: 30 !important;
        }
        
        @media (min-width: 1024px) {
            .mobile-overlay {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
            }
        }
        
        /* Additional fixes for content visibility */
        * {
            -webkit-backdrop-filter: none !important;
            backdrop-filter: none !important;
        }
        
        /* Only allow backdrop filter on specific elements */
        .sidebar-header-bg,
        .glassmorphism,
        .user-info-bg {
            -webkit-backdrop-filter: blur(4px) !important;
            backdrop-filter: blur(4px) !important;
        }
        
        /* Enhanced Sidebar Styles */
        .sidebar {
            width: 16rem;
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 50;
            background: linear-gradient(180deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            transform: translateX(-100%);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .sidebar.open {
            transform: translateX(0);
        }
        
        @media (min-width: 1024px) {
            .sidebar {
                position: fixed;
                transform: translateX(0) !important;
                z-index: 50;
            }
        }
        
        /* Main content area adjustments */
        .main-content {
            flex: 1;
            margin-left: 0;
            min-height: 100vh;
            transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @media (min-width: 1024px) {
            .main-content {
                margin-left: 16rem;
            }
        }
        
        /* Sidebar Navigation Items */
        .sidebar-nav-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            color: #d1d5db;
            text-decoration: none;
            border-radius: 0.75rem;
            font-size: 0.875rem;
            font-weight: 500;
            border-left: 4px solid transparent;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        .sidebar-nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.05);
            opacity: 0;
            transition: opacity 0.2s ease;
        }
        
        .sidebar-nav-item:hover::before {
            opacity: 1;
        }
        
        .sidebar-nav-item:hover {
            color: #ffffff;
            transform: translateX(4px);
        }
        
        .sidebar-nav-item.active {
            color: #ffffff;
            background: linear-gradient(90deg, #3b82f6 0%, #7c3aed 100%);
            border-left-color: #60a5fa;
            box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.25);
        }
        
        .sidebar-nav-item .icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2rem;
            height: 2rem;
            border-radius: 0.5rem;
            margin-right: 0.75rem;
            background: rgba(255, 255, 255, 0.1);
            transition: all 0.2s ease;
        }
        
        .sidebar-nav-item.active .icon {
            background: rgba(255, 255, 255, 0.2);
        }
        
        /* Sidebar scrollbar styling */
        .sidebar-nav::-webkit-scrollbar {
            width: 4px;
        }
        
        .sidebar-nav::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
        }
        
        .sidebar-nav::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
        }
        
        .sidebar-nav::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
        
        /* Responsive improvements */
        @media (max-width: 1023px) {
            .sidebar {
                backdrop-filter: blur(8px);
                -webkit-backdrop-filter: blur(8px);
            }
        }
        
        /* Enhanced user info section */
        .sidebar-user-info {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 0.75rem;
            padding: 0.75rem;
            margin-bottom: 1rem;
            transition: all 0.2s ease;
        }
        
        .sidebar-user-info:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
        }
        
        /* Enhanced logout button */
        .sidebar-logout-btn {
            width: 100%;
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: #d1d5db;
            background: transparent;
            border: 1px solid transparent;
            border-radius: 0.75rem;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .sidebar-logout-btn:hover {
            color: #ffffff;
            background: rgba(239, 68, 68, 0.2);
            border-color: rgba(239, 68, 68, 0.3);
        }
        
        .sidebar-logout-btn .icon {
            background: rgba(255, 255, 255, 0.1);
            transition: all 0.2s ease;
        }
        
        .sidebar-logout-btn:hover .icon {
            background: rgba(239, 68, 68, 0.2);
        }
    </style>
</head>
<body class="font-sans antialiased bg-gray-50" data-theme="light">
    <div x-data="{ sidebarOpen: false }" class="flex h-screen bg-gray-50">
        <!-- Sidebar -->
        <div class="sidebar"
             :class="{ 'open': sidebarOpen }">

            <!-- Sidebar Header -->
            <div class="flex items-center justify-between h-20 relative overflow-hidden px-4 sidebar-header-bg"
                 style="background: linear-gradient(90deg, #2563eb 0%, #7c3aed 50%, #4338ca 100%);">
                <!-- Background Pattern -->
                <div class="absolute inset-0" style="background: rgba(0, 0, 0, 0.1);"></div>
                <div class="absolute inset-0" style="background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.05) 50%, transparent 100%);"></div>

                <div class="flex items-center relative z-10">
                    <div class="flex items-center justify-center w-10 h-10 rounded-xl mr-3 glassmorphism"
                         style="background: rgba(255, 255, 255, 0.2); border: 1px solid rgba(255, 255, 255, 0.2);">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-white">SMS City</h1>
                        <p class="text-xs text-blue-100" style="opacity: 0.9;">Admin Panel</p>
                    </div>
                </div>

                <!-- Close button for mobile -->
                <button @click="sidebarOpen = false"
                        class="lg:hidden p-2 text-white rounded-lg relative z-10"
                        style="transition: all 0.2s;"
                        onmouseover="this.style.background='rgba(255,255,255,0.2)'"
                        onmouseout="this.style.background='transparent'">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 mt-8 px-4 relative overflow-y-auto">
                <div class="mb-4">
                    <p class="text-xs font-semibold uppercase tracking-wider px-4" style="color: #9ca3af;">Main Menu</p>
                </div>
                <div class="space-y-2 pb-24">
                    <!-- Dashboard -->
                    <a href="{{ route('admin.dashboard') }}"
                       @click="sidebarOpen = false"
                       class="sidebar-nav-item {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                        <div class="icon">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"/>
                            </svg>
                        </div>
                        <span>Dashboard</span>
                    </a>
                    
                    <!-- Clients -->
                    <a href="{{ route('admin.clients.index') }}"
                       @click="sidebarOpen = false"
                       class="sidebar-nav-item {{ request()->routeIs('admin.clients.*') ? 'active' : '' }}">
                        <div class="icon">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                        <span>Clients</span>
                    </a>
                    
                    <!-- SMS Messages -->
                    <a href="{{ route('admin.sms.index') }}"
                       @click="sidebarOpen = false"
                       class="sidebar-nav-item {{ request()->routeIs('admin.sms.*') ? 'active' : '' }}">
                        <div class="icon">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                        </div>
                        <span>SMS Messages</span>
                    </a>
                    
                    <!-- Profile -->
                    <a href="{{ route('admin.profile') }}"
                       @click="sidebarOpen = false"
                       class="sidebar-nav-item {{ request()->routeIs('admin.profile') ? 'active' : '' }}">
                        <div class="icon">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <span>Profile</span>
                    </a>
                </div>

                <!-- User Info & Logout - Fixed Position -->
                <div class="absolute bottom-0 left-0 right-0 p-4"
                     style="border-top: 1px solid rgba(255,255,255,0.1); background: linear-gradient(180deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);">
                    <!-- User Info -->
                    <div class="sidebar-user-info">
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-10 h-10 rounded-full mr-3"
                                 style="background: linear-gradient(90deg, #3b82f6 0%, #7c3aed 100%); box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                                <span class="text-sm font-medium text-white">{{ substr(Auth::user()->name, 0, 1) }}</span>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-white truncate">{{ Auth::user()->name }}</p>
                                <p class="text-xs text-gray-300 truncate">Administrator</p>
                            </div>
                            <div class="w-2 h-2 rounded-full" style="background: #22c55e; box-shadow: 0 0 4px rgba(34, 197, 94, 0.5);"></div>
                        </div>
                    </div>

                    <!-- Logout Button -->
                    <form method="POST" action="{{ route('admin.logout') }}">
                        @csrf
                        <button type="submit" class="sidebar-logout-btn">
                            <div class="icon mr-3">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                            </div>
                            <span class="font-medium">Logout</span>
                        </button>
                    </form>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content flex flex-col overflow-hidden content-area">
            <!-- Top Header -->
            <header class="sticky top-0 z-40 glassmorphism"
                    style="background: var(--bg-secondary); box-shadow: var(--shadow-primary); border-bottom: 1px solid var(--border-primary);">
                <div class="flex items-center justify-between px-6 py-4">
                    <!-- Mobile menu button -->
                    <button @click="sidebarOpen = !sidebarOpen"
                            class="lg:hidden p-2 rounded-xl transition-colors duration-200"
                            style="color: var(--text-secondary); border: none; background: transparent;"
                            onmouseover="this.style.color='var(--text-primary)'; this.style.background='var(--bg-tertiary)';"
                            onmouseout="this.style.color='var(--text-secondary)'; this.style.background='transparent';"
                            onfocus="this.style.outline='2px solid #3b82f6'; this.style.outlineOffset='2px';"
                            onblur="this.style.outline='none';">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path x-show="!sidebarOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            <path x-show="sidebarOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>

                    <!-- Page Title with Breadcrumb -->
                    <div class="flex-1 lg:ml-0 ml-4">
                        <div class="flex items-center space-x-2 text-sm mb-1" style="color: var(--text-secondary);">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                            </svg>
                            <span>Admin</span>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                            <span style="color: var(--text-primary); font-weight: 500;">@yield('page-title', 'Dashboard')</span>
                        </div>
                        <h2 class="text-2xl font-bold" style="color: var(--text-primary);">@yield('page-title', 'Dashboard')</h2>
                    </div>

                    <!-- Header Actions -->
                    <div class="flex items-center space-x-4">
                        <!-- Dark Mode Toggle -->
                        <div class="flex items-center">
                            <div onclick="toggleDarkMode()" 
                                 class="dark-mode-toggle"
                                 title="Toggle Dark Mode">
                            </div>
                        </div>
                        
                        <!-- Notifications -->
                        <button class="relative p-2 rounded-xl transition-colors duration-200"
                                style="color: var(--text-secondary); background: transparent; border: none;"
                                onmouseover="this.style.color='var(--text-primary)'; this.style.background='var(--bg-tertiary)';"
                                onmouseout="this.style.color='var(--text-secondary)'; this.style.background='transparent';">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.5a6 6 0 0 1 6 6v2l1.5 3h-15l1.5-3v-2a6 6 0 0 1 6-6z"></path>
                            </svg>
                            <span class="absolute top-1 right-1 w-2 h-2 rounded-full"
                                  style="background: #ef4444;"></span>
                        </button>

                        <!-- User Dropdown -->
                        <div class="relative">
                            <button onclick="toggleUserDropdown(this)"
                                    id="userDropdownButton"
                                    class="flex items-center space-x-3 p-2 rounded-xl transition-colors duration-200"
                                    style="background: transparent; border: none;"
                                    onmouseover="this.style.background='#f3f4f6';"
                                    onmouseout="this.style.background='transparent';"
                                    aria-expanded="false"
                                    aria-haspopup="true">
                                <div class="flex items-center justify-center w-10 h-10 rounded-full"
                                     style="background: linear-gradient(90deg, #2563eb 0%, #7c3aed 100%); box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);">
                                    <span class="text-sm font-medium text-white">{{ substr(Auth::user()->name, 0, 1) }}</span>
                                </div>
                                <div class="hidden md:block text-left">
                                    <p class="text-sm font-medium" style="color: #111827;">{{ Auth::user()->name }}</p>
                                    <p class="text-xs" style="color: #6b7280;">Administrator</p>
                                </div>
                                <svg class="w-4 h-4 transition-transform duration-200" id="dropdownArrow" style="color: #6b7280;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <!-- Enhanced Dropdown Menu -->
                            <div id="userDropdownMenu"
                                 class="absolute right-0 mt-3 w-64 rounded-2xl py-2 opacity-0 invisible transform scale-95 transition-all duration-200"
                                 style="background: var(--bg-secondary); box-shadow: var(--shadow-large); border: 1px solid var(--border-primary); z-index: 9999;">
                                
                                <!-- User Info Header -->
                                <div class="px-5 py-4 border-b border-gray-100">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex items-center justify-center w-12 h-12 rounded-full"
                                             style="background: linear-gradient(90deg, #2563eb 0%, #7c3aed 100%); box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                                            <span class="text-lg font-semibold text-white">{{ substr(Auth::user()->name, 0, 1) }}</span>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <p class="text-sm font-semibold text-gray-900 truncate">{{ Auth::user()->name }}</p>
                                            <p class="text-xs text-gray-500 truncate">{{ Auth::user()->email }}</p>
                                            <div class="flex items-center mt-1">
                                                <div class="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                                                <span class="text-xs text-green-600 font-medium">Administrator</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Menu Items -->
                                <div class="py-2">
                                    <!-- Dashboard -->
                                    <a href="{{ route('admin.dashboard') }}"
                                       class="group flex items-center px-5 py-3 text-sm transition-all duration-200"
                                       style="color: #374151; text-decoration: none;"
                                       onmouseover="this.style.background='linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%)';"
                                       onmouseout="this.style.background='transparent';">
                                        <div class="flex items-center justify-center w-8 h-8 rounded-lg mr-3"
                                             style="background: #e0e7ff; color: #4338ca;">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <p class="font-medium text-gray-900">Dashboard</p>
                                            <p class="text-xs text-gray-500">View overview</p>
                                        </div>
                                    </a>

                                    <!-- My Profile -->
                                    <a href="{{ route('admin.profile') }}"
                                       class="group flex items-center px-5 py-3 text-sm transition-all duration-200"
                                       style="color: #374151; text-decoration: none;"
                                       onmouseover="this.style.background='linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%)';"
                                       onmouseout="this.style.background='transparent';">
                                        <div class="flex items-center justify-center w-8 h-8 rounded-lg mr-3"
                                             style="background: #dcfce7; color: #166534;">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <p class="font-medium text-gray-900">My Profile</p>
                                            <p class="text-xs text-gray-500">Manage account</p>
                                        </div>
                                    </a>

                                    <!-- Settings -->
                                    <a href="{{ route('admin.settings') }}"
                                       class="group flex items-center px-5 py-3 text-sm transition-all duration-200"
                                       style="color: #374151; text-decoration: none;"
                                       onmouseover="this.style.background='linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%)';"
                                       onmouseout="this.style.background='transparent';">
                                        <div class="flex items-center justify-center w-8 h-8 rounded-lg mr-3"
                                             style="background: #fef3c7; color: #92400e;">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <p class="font-medium text-gray-900">Settings</p>
                                            <p class="text-xs text-gray-500">System preferences</p>
                                        </div>
                                    </a>

                                    <!-- Help & Support -->
                                    <a href="#"
                                       class="group flex items-center px-5 py-3 text-sm transition-all duration-200"
                                       style="color: #374151; text-decoration: none;"
                                       onmouseover="this.style.background='linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%)';"
                                       onmouseout="this.style.background='transparent';">
                                        <div class="flex items-center justify-center w-8 h-8 rounded-lg mr-3"
                                             style="background: #e0f2fe; color: #0891b2;">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <p class="font-medium text-gray-900">Help & Support</p>
                                            <p class="text-xs text-gray-500">Get assistance</p>
                                        </div>
                                    </a>
                                </div>

                                <!-- Divider -->
                                <hr style="margin: 8px 0; border-color: #e5e7eb;">

                                <!-- Logout -->
                                <div class="py-2">
                                    <form method="POST" action="{{ route('admin.logout') }}">
                                        @csrf
                                        <button type="submit"
                                                class="group flex items-center w-full px-5 py-3 text-sm transition-all duration-200"
                                                style="color: #dc2626; background: transparent; border: none; text-align: left;"
                                                onmouseover="this.style.background='linear-gradient(90deg, #fef2f2 0%, #fecaca 20%)';"
                                                onmouseout="this.style.background='transparent';">
                                            <div class="flex items-center justify-center w-8 h-8 rounded-lg mr-3"
                                                 style="background: #fee2e2; color: #dc2626;">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                                </svg>
                                            </div>
                                            <div class="flex-1">
                                                <p class="font-medium text-red-600">Sign Out</p>
                                                <p class="text-xs text-red-400">End your session</p>
                                            </div>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto"
                  style="background: var(--bg-primary) !important; position: relative; z-index: 20;">
                <div class="container mx-auto px-6 py-8" style="background: transparent !important; position: relative; z-index: 25;">
                    <!-- Success/Error Messages -->
                    @if (session('success'))
                        <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                            <div class="flex">
                                <svg class="w-5 h-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                <p class="text-sm text-green-600">{{ session('success') }}</p>
                            </div>
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                            <div class="flex">
                                <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                </svg>
                                <p class="text-sm text-red-600">{{ session('error') }}</p>
                            </div>
                        </div>
                    @endif

                    @yield('content')
                </div>
            </main>
        </div>
    </div>

    <!-- Mobile sidebar overlay -->
    <div x-show="sidebarOpen && window.innerWidth < 1024"
         @click="sidebarOpen = false"
         class="mobile-overlay fixed inset-0 z-30 lg:hidden"
         style="background: rgba(0, 0, 0, 0.5);"
         x-transition:enter="transition-all ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-all ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0">
    </div>

    <!-- JavaScript for Dropdown and Sidebar -->
    <script>
        let dropdownOpen = false;
        
        function toggleUserDropdown(button) {
            const dropdown = document.getElementById('userDropdownMenu');
            const arrow = document.getElementById('dropdownArrow');
            
            if (!dropdownOpen) {
                // Show dropdown
                dropdown.classList.remove('opacity-0', 'invisible', 'scale-95');
                dropdown.classList.add('opacity-100', 'visible', 'scale-100');
                arrow.style.transform = 'rotate(180deg)';
                button.setAttribute('aria-expanded', 'true');
                dropdownOpen = true;
            } else {
                // Hide dropdown
                dropdown.classList.add('opacity-0', 'invisible', 'scale-95');
                dropdown.classList.remove('opacity-100', 'visible', 'scale-100');
                arrow.style.transform = 'rotate(0deg)';
                button.setAttribute('aria-expanded', 'false');
                dropdownOpen = false;
            }
        }
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('userDropdownMenu');
            const button = document.getElementById('userDropdownButton');
            
            if (!button.contains(event.target) && !dropdown.contains(event.target)) {
                if (dropdownOpen) {
                    dropdown.classList.add('opacity-0', 'invisible', 'scale-95');
                    dropdown.classList.remove('opacity-100', 'visible', 'scale-100');
                    document.getElementById('dropdownArrow').style.transform = 'rotate(0deg)';
                    button.setAttribute('aria-expanded', 'false');
                    dropdownOpen = false;
                }
            }
        });
        
        // Close dropdown on escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' && dropdownOpen) {
                const dropdown = document.getElementById('userDropdownMenu');
                const button = document.getElementById('userDropdownButton');
                dropdown.classList.add('opacity-0', 'invisible', 'scale-95');
                dropdown.classList.remove('opacity-100', 'visible', 'scale-100');
                document.getElementById('dropdownArrow').style.transform = 'rotate(0deg)';
                button.setAttribute('aria-expanded', 'false');
                dropdownOpen = false;
            }
        });
        
        // Enhanced sidebar functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Handle sidebar navigation active states
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.sidebar-nav-item');
            
            navItems.forEach(item => {
                const href = item.getAttribute('href');
                if (href && currentPath.includes(href)) {
                    item.classList.add('active');
                }
            });
            
            // Smooth sidebar animations for better UX
            const sidebar = document.querySelector('.sidebar');
            if (sidebar) {
                sidebar.addEventListener('transitionend', function() {
                    // Callback after sidebar animation completes
                    console.log('Sidebar animation completed');
                });
            }
            
            // Auto-close sidebar on route navigation for mobile
            const sidebarLinks = document.querySelectorAll('.sidebar-nav-item');
            sidebarLinks.forEach(link => {
                link.addEventListener('click', function() {
                    if (window.innerWidth < 1024) {
                        // Close sidebar on mobile after navigation
                        setTimeout(() => {
                            const sidebarOpenState = Alpine.store && Alpine.store('sidebarOpen');
                            if (sidebarOpenState) {
                                Alpine.store('sidebarOpen', false);
                            }
                        }, 100);
                    }
                });
            });
        });
        
        // Dark Mode Functions
        function toggleDarkMode() {
            console.log('Dark mode toggle clicked'); // Debug log
            const body = document.body;
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            console.log('Current theme:', currentTheme, 'New theme:', newTheme); // Debug log
            
            body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            
            // Add transition effect
            body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
            setTimeout(() => {
                body.style.transition = '';
            }, 300);
        }
        
        // Initialize theme on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing theme'); // Debug log
            const savedTheme = localStorage.getItem('theme');
            const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
            const theme = savedTheme || systemTheme;
            
            console.log('Saved theme:', savedTheme, 'System theme:', systemTheme, 'Final theme:', theme); // Debug log
            document.body.setAttribute('data-theme', theme);
        });
        
        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
            if (!localStorage.getItem('theme')) {
                document.body.setAttribute('data-theme', e.matches ? 'dark' : 'light');
            }
        });
        
        // Handle responsive behavior
        window.addEventListener('resize', function() {
            const sidebar = document.querySelector('.sidebar');
            if (window.innerWidth >= 1024) {
                // Desktop view - ensure sidebar is visible
                if (sidebar) {
                    sidebar.classList.remove('open');
                }
            }
        });
        
        // Keyboard navigation support
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                // Close sidebar on escape
                const sidebar = document.querySelector('.sidebar');
                if (sidebar && sidebar.classList.contains('open')) {
                    sidebar.classList.remove('open');
                }
            }
        });
    </script>
</body>
</html>
