<x-client-layout>
    <x-slot name="header">
        Client Dashboard
    </x-slot>

    <style>
        /* Modern Dashboard Styles */
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0;
        }

        .welcome-banner {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
            color: #ffffff;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .welcome-banner::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(5deg); }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--card-bg);
            border-radius: 1rem;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--accent-primary);
            transition: width 0.3s ease;
        }

        .stat-card:hover::before {
            width: 6px;
        }

        .stat-card.green::before { background: #10b981; }
        .stat-card.emerald::before { background: #059669; }
        .stat-card.red::before { background: #ef4444; }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ffffff;
            font-size: 1.25rem;
        }

        .stat-icon.blue { background: linear-gradient(135deg, #3b82f6, #1d4ed8); }
        .stat-icon.green { background: linear-gradient(135deg, #10b981, #047857); }
        .stat-icon.emerald { background: linear-gradient(135deg, #059669, #065f46); }
        .stat-icon.red { background: linear-gradient(135deg, #ef4444, #dc2626); }

        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .action-card {
            background: var(--card-bg);
            border-radius: 1rem;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .action-card:hover {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .action-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 0.75rem;
            margin-bottom: 0.75rem;
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .action-item:last-child {
            margin-bottom: 0;
        }

        .action-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .action-item.blue {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(29, 78, 216, 0.1));
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .action-item.green {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(4, 120, 87, 0.1));
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .action-item.purple {
            background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(109, 40, 217, 0.1));
            border: 1px solid rgba(139, 92, 246, 0.2);
        }

        .action-item.orange {
            background: linear-gradient(135deg, rgba(249, 115, 22, 0.1), rgba(234, 88, 12, 0.1));
            border: 1px solid rgba(249, 115, 22, 0.2);
        }

        .balance-card {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            border-radius: 1rem;
            padding: 2rem;
            color: #ffffff;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }

        .balance-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .balance-card::after {
            content: '';
            position: absolute;
            bottom: -30%;
            left: -10%;
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: #ffffff;
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: var(--bg-secondary);
            border-radius: 0.75rem;
            margin-bottom: 0.75rem;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .activity-item:hover {
            background: var(--accent-primary);
            color: #ffffff;
            transform: translateX(5px);
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .status-dot.success { background: #10b981; }
        .status-dot.error { background: #ef4444; }
        .status-dot.pending { background: #f59e0b; }

        .chart-container {
            background: var(--card-bg);
            border-radius: 1rem;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .chart-legend {
            display: grid;
            gap: 0.75rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.75rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .legend-item:hover {
            background: var(--bg-secondary);
            border-color: var(--border-color);
        }

        .legend-item.success {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(4, 120, 87, 0.1));
        }

        .legend-item.error {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
        }

        .legend-item.pending {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(217, 119, 6, 0.1));
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .welcome-banner {
                padding: 1.5rem;
                text-align: center;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .content-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .stat-card {
                padding: 1rem;
            }
        }

        /* Dark mode adjustments */
        [data-theme="dark"] .welcome-banner {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
        }

        [data-theme="dark"] .balance-card {
            background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
        }
    </style>

    <div class="dashboard-container">
        <!-- Welcome Banner -->
        <div class="welcome-banner">
            <div style="display: flex; flex-direction: column; align-items: flex-start; position: relative; z-index: 1;">
                <div style="margin-bottom: 1rem;">
                    <h2 style="font-size: 2rem; font-weight: 700; margin: 0 0 0.5rem 0;">
                        Welcome back, {{ Auth::guard('client')->user()->name }}! 👋
                    </h2>
                    <p style="color: rgba(255, 255, 255, 0.8); font-size: 1.125rem; margin: 0;">
                        Here's your SMS dashboard overview for today
                    </p>
                    <div style="display: flex; align-items: center; margin-top: 0.75rem; color: rgba(255, 255, 255, 0.7);">
                        <i class="fas fa-clock" style="margin-right: 0.5rem;"></i>
                        <span style="font-size: 0.875rem;">Last login: {{ Auth::guard('client')->user()->updated_at->diffForHumans() }}</span>
                    </div>
                </div>
                <div style="position: absolute; top: 0; right: 0;">
                    <div style="background: rgba(255, 255, 255, 0.1); border-radius: 0.75rem; padding: 1rem; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.2);">
                        <div style="font-size: 1.5rem; font-weight: 700; color: #ffffff;">{{ now()->format('d M Y') }}</div>
                        <div style="color: rgba(255, 255, 255, 0.8); font-size: 0.875rem;">{{ now()->format('l') }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="stats-grid">
            <!-- Total SMS -->
            <div class="stat-card">
                <div class="stat-header">
                    <div>
                        <h3 style="font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); text-transform: uppercase; letter-spacing: 0.05em; margin: 0;">Total SMS</h3>
                        <p style="font-size: 2rem; font-weight: 700; color: var(--text-primary); margin: 0.5rem 0;">{{ number_format($totalSms) }}</p>
                        @php
                            $successRate = $totalSms > 0 ? round(($smsStats['sent'] / $totalSms) * 100, 1) : 0;
                        @endphp
                        <p style="font-size: 0.875rem; color: var(--text-secondary); margin: 0; display: flex; align-items: center; font-weight: 500;">
                            <i class="fas fa-chart-line" style="margin-right: 0.25rem; color: #3b82f6;"></i> {{ $successRate }}% success rate
                        </p>
                    </div>
                    <div class="stat-icon blue">
                        <i class="fas fa-comment-sms"></i>
                    </div>
                </div>
            </div>

            <!-- Total Charge -->
            <div class="stat-card green">
                <div class="stat-header">
                    <div>
                        <h3 style="font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); text-transform: uppercase; letter-spacing: 0.05em; margin: 0;">Total Spent</h3>
                        <p style="font-size: 2rem; font-weight: 700; color: var(--text-primary); margin: 0.5rem 0;">৳{{ number_format($totalCharge, 2) }}</p>
                        @php
                            $avgCost = $totalSms > 0 ? round($totalCharge / $totalSms, 2) : 0;
                        @endphp
                        <p style="font-size: 0.875rem; color: var(--text-secondary); margin: 0; display: flex; align-items: center; font-weight: 500;">
                            <i class="fas fa-calculator" style="margin-right: 0.25rem; color: #10b981;"></i> ৳{{ $avgCost }} avg per SMS
                        </p>
                    </div>
                    <div class="stat-icon green">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
            </div>

            <!-- SMS Sent -->
            <div class="stat-card emerald">
                <div class="stat-header">
                    <div>
                        <h3 style="font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); text-transform: uppercase; letter-spacing: 0.05em; margin: 0;">SMS Delivered</h3>
                        <p style="font-size: 2rem; font-weight: 700; color: var(--text-primary); margin: 0.5rem 0;">{{ number_format($smsStats['sent']) }}</p>
                        <p style="font-size: 0.875rem; color: var(--text-secondary); margin: 0; display: flex; align-items: center; font-weight: 500;">
                            <i class="fas fa-check-circle" style="margin-right: 0.25rem; color: #059669;"></i> Successfully delivered
                        </p>
                    </div>
                    <div class="stat-icon emerald">
                        <i class="fas fa-paper-plane"></i>
                    </div>
                </div>
            </div>

            <!-- SMS Failed -->
            <div class="stat-card red">
                <div class="stat-header">
                    <div>
                        <h3 style="font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); text-transform: uppercase; letter-spacing: 0.05em; margin: 0;">SMS Failed</h3>
                        <p style="font-size: 2rem; font-weight: 700; color: var(--text-primary); margin: 0.5rem 0;">{{ number_format($smsStats['failed']) }}</p>
                        @php
                            $failureRate = $totalSms > 0 ? round(($smsStats['failed'] / $totalSms) * 100, 1) : 0;
                        @endphp
                        <p style="font-size: 0.875rem; color: var(--text-secondary); margin: 0; display: flex; align-items: center; font-weight: 500;">
                            <i class="fas fa-exclamation-triangle" style="margin-right: 0.25rem; color: #ef4444;"></i> {{ $failureRate }}% failure rate
                        </p>
                    </div>
                    <div class="stat-icon red">
                        <i class="fas fa-times-circle"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Grid -->
        <div class="content-grid">
            <!-- Quick Actions -->
            <div class="action-card">
                <h3 style="font-size: 1.125rem; font-weight: 600; color: var(--text-primary); margin: 0 0 1.5rem 0; display: flex; align-items: center;">
                    <i class="fas fa-bolt" style="color: #3b82f6; margin-right: 0.5rem;"></i>
                    Quick Actions
                </h3>
                <div style="display: grid; gap: 0.75rem;">
                    <a href="#" class="action-item blue">
                        <div style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); padding: 0.75rem; border-radius: 0.5rem; margin-right: 1rem; color: #ffffff; display: flex; align-items: center; justify-content: center; width: 48px; height: 48px;">
                            <i class="fas fa-paper-plane" style="font-size: 1.125rem;"></i>
                        </div>
                        <div style="flex: 1;">
                            <span style="font-weight: 600; color: var(--text-primary);">Send SMS</span>
                            <p style="font-size: 0.875rem; color: var(--text-secondary); margin: 0;">Send instant messages</p>
                        </div>
                        <i class="fas fa-chevron-right" style="color: var(--text-secondary);"></i>
                    </a>

                    <a href="{{ route('client.sms-history') }}" class="action-item green">
                        <div style="background: linear-gradient(135deg, #10b981, #047857); padding: 0.75rem; border-radius: 0.5rem; margin-right: 1rem; color: #ffffff; display: flex; align-items: center; justify-content: center; width: 48px; height: 48px;">
                            <i class="fas fa-history" style="font-size: 1.125rem;"></i>
                        </div>
                        <div style="flex: 1;">
                            <span style="font-weight: 600; color: var(--text-primary);">SMS History</span>
                            <p style="font-size: 0.875rem; color: var(--text-secondary); margin: 0;">View past messages</p>
                        </div>
                        <i class="fas fa-chevron-right" style="color: var(--text-secondary);"></i>
                    </a>

                    <a href="#" class="action-item purple">
                        <div style="background: linear-gradient(135deg, #8b5cf6, #6d28d9); padding: 0.75rem; border-radius: 0.5rem; margin-right: 1rem; color: #ffffff; display: flex; align-items: center; justify-content: center; width: 48px; height: 48px;">
                            <i class="fas fa-address-book" style="font-size: 1.125rem;"></i>
                        </div>
                        <div style="flex: 1;">
                            <span style="font-weight: 600; color: var(--text-primary);">Contacts</span>
                            <p style="font-size: 0.875rem; color: var(--text-secondary); margin: 0;">Manage contacts</p>
                        </div>
                        <i class="fas fa-chevron-right" style="color: var(--text-secondary);"></i>
                    </a>

                    <a href="#" class="action-item orange">
                        <div style="background: linear-gradient(135deg, #f97316, #ea580c); padding: 0.75rem; border-radius: 0.5rem; margin-right: 1rem; color: #ffffff; display: flex; align-items: center; justify-content: center; width: 48px; height: 48px;">
                            <i class="fas fa-credit-card" style="font-size: 1.125rem;"></i>
                        </div>
                        <div style="flex: 1;">
                            <span style="font-weight: 600; color: var(--text-primary);">Add Credits</span>
                            <p style="font-size: 0.875rem; color: var(--text-secondary); margin: 0;">Top up balance</p>
                        </div>
                        <i class="fas fa-chevron-right" style="color: var(--text-secondary);"></i>
                    </a>
                </div>
            </div>

            <!-- Account Balance -->
            <div class="balance-card">
                <div style="position: relative; z-index: 10;">
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0 0 1rem 0; display: flex; align-items: center;">
                        <i class="fas fa-wallet" style="margin-right: 0.5rem;"></i>
                        Account Balance
                    </h3>
                    
                    @php
                        // Calculate estimated balance based on spending
                        $estimatedBalance = max(1000 - $totalCharge, 0);
                        $estimatedCredits = max(floor($estimatedBalance / 0.25), 0); // Assuming 0.25 per SMS
                    @endphp
                    
                    <div style="font-size: 2rem; font-weight: 700; margin-bottom: 0.5rem;">৳{{ number_format($estimatedBalance, 2) }}</div>
                    <p style="color: rgba(255, 255, 255, 0.8); margin: 0 0 1rem 0; display: flex; align-items: center;">
                        <i class="fas fa-coins" style="margin-right: 0.5rem;"></i>
                        Available SMS Credits: {{ number_format($estimatedCredits) }}
                    </p>
                    
                    <!-- Progress bar for balance -->
                    @php
                        $balancePercentage = ($estimatedBalance / 1000) * 100;
                    @endphp
                    <div style="margin-bottom: 1rem;">
                        <div style="display: flex; justify-content: space-between; font-size: 0.875rem; margin-bottom: 0.25rem;">
                            <span>Balance Usage</span>
                            <span>{{ number_format($balancePercentage, 1) }}%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: {{ $balancePercentage }}%"></div>
                        </div>
                    </div>
                    
                    <button style="background: #ffffff; color: #3b82f6; padding: 0.75rem 1.5rem; border-radius: 0.5rem; font-weight: 600; border: none; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center; width: 100%; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);" onmouseover="this.style.background='#f8fafc';" onmouseout="this.style.background='#ffffff';">
                        <i class="fas fa-plus" style="margin-right: 0.5rem;"></i>Add Credits
                    </button>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="action-card">
                <h3 style="font-size: 1.125rem; font-weight: 600; color: var(--text-primary); margin: 0 0 1rem 0; display: flex; align-items: center;">
                    <i class="fas fa-clock" style="color: #10b981; margin-right: 0.5rem;"></i>
                    Recent Activity
                </h3>
                <div style="display: grid; gap: 0.75rem;">
                    @if($recentSms->count() > 0)
                        @foreach($recentSms->take(3) as $sms)
                            <div class="activity-item">
                                @if($sms->status == 'sent' || $sms->status == '445000')
                                    <div class="status-dot success"></div>
                                @elseif($sms->status == 'failed')
                                    <div class="status-dot error"></div>
                                @else
                                    <div class="status-dot pending"></div>
                                @endif
                                <div style="flex: 1; min-width: 0;">
                                    <p style="font-size: 0.875rem; font-weight: 500; color: var(--text-primary); margin: 0; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                        SMS to {{ substr($sms->to_number, 0, 5) }}****
                                    </p>
                                    <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0;">{{ $sms->created_at->diffForHumans() }}</p>
                                </div>
                                <div style="font-size: 0.75rem; font-weight: 600; color: var(--text-primary);">
                                    ৳{{ number_format($sms->charge, 2) }}
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div style="text-align: center; padding: 2rem 0;">
                            <i class="fas fa-history" style="font-size: 3rem; color: var(--text-secondary); margin-bottom: 1rem; opacity: 0.5;"></i>
                            <p style="color: var(--text-primary); font-weight: 500; margin: 0;">No recent activity</p>
                            <p style="font-size: 0.875rem; color: var(--text-secondary); margin: 0;">Your SMS activity will appear here</p>
                        </div>
                    @endif

                    @if($recentSms->count() > 3)
                        <div style="padding-top: 0.75rem; border-top: 1px solid var(--border-color);">
                            <a href="{{ route('client.sms-history') }}" style="font-size: 0.875rem; color: #3b82f6; font-weight: 500; display: flex; align-items: center; justify-content: center; text-decoration: none; transition: color 0.2s ease;" onmouseover="this.style.color='#1d4ed8';" onmouseout="this.style.color='#3b82f6';">
                                View all activity <i class="fas fa-arrow-right" style="margin-left: 0.25rem;"></i>
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 2rem; margin-bottom: 2rem;">
            <!-- SMS Statistics Chart -->
            <div class="chart-container">
                <h3 style="font-size: 1.125rem; font-weight: 600; color: var(--text-primary); margin: 0 0 1.5rem 0; display: flex; align-items: center;">
                    <i class="fas fa-chart-pie" style="color: #3b82f6; margin-right: 0.5rem;"></i>
                    SMS Statistics
                </h3>

                @if($totalSms > 0)
                    <div style="position: relative;">
                        <!-- Pie Chart Simulation -->
                        <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem;">
                            <div style="position: relative; width: 8rem; height: 8rem;">
                                @php
                                    $sentPercentage = ($smsStats['sent'] / $totalSms) * 100;
                                    $failedPercentage = ($smsStats['failed'] / $totalSms) * 100;
                                    $pendingPercentage = ($smsStats['pending'] / $totalSms) * 100;
                                @endphp

                                <!-- Background circle -->
                                <div style="width: 8rem; height: 8rem; border-radius: 50%; background: linear-gradient(135deg, #10b981, #059669); display: flex; align-items: center; justify-content: center; color: #ffffff; font-weight: 700; font-size: 1.125rem; box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);">
                                    {{ number_format($sentPercentage, 1) }}%
                                </div>
                            </div>
                        </div>

                        <!-- Legend -->
                        <div class="chart-legend">
                            <div class="legend-item success">
                                <div style="display: flex; align-items: center;">
                                    <div style="width: 12px; height: 12px; background: #10b981; border-radius: 50%; margin-right: 0.75rem;"></div>
                                    <span style="font-weight: 500; color: var(--text-primary);">Delivered</span>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-weight: 700; color: var(--text-primary);">{{ number_format($smsStats['sent']) }}</div>
                                    <div style="font-size: 0.875rem; color: var(--text-secondary);">{{ number_format($sentPercentage, 1) }}%</div>
                                </div>
                            </div>

                            <div class="legend-item error">
                                <div style="display: flex; align-items: center;">
                                    <div style="width: 12px; height: 12px; background: #ef4444; border-radius: 50%; margin-right: 0.75rem;"></div>
                                    <span style="font-weight: 500; color: var(--text-primary);">Failed</span>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-weight: 700; color: var(--text-primary);">{{ number_format($smsStats['failed']) }}</div>
                                    <div style="font-size: 0.875rem; color: var(--text-secondary);">{{ number_format($failedPercentage, 1) }}%</div>
                                </div>
                            </div>

                            <div class="legend-item pending">
                                <div style="display: flex; align-items: center;">
                                    <div style="width: 12px; height: 12px; background: #f59e0b; border-radius: 50%; margin-right: 0.75rem;"></div>
                                    <span style="font-weight: 500; color: var(--text-primary);">Pending</span>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-weight: 700; color: var(--text-primary);">{{ number_format($smsStats['pending']) }}</div>
                                    <div style="font-size: 0.875rem; color: var(--text-secondary);">{{ number_format($pendingPercentage, 1) }}%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                @else
                    <div style="text-align: center; padding: 3rem 0;">
                        <i class="fas fa-chart-pie" style="font-size: 4rem; color: var(--text-secondary); margin-bottom: 1rem; opacity: 0.3;"></i>
                        <p style="color: var(--text-primary); font-weight: 500; margin: 0;">No data available</p>
                        <p style="font-size: 0.875rem; color: var(--text-secondary); margin: 0;">Send your first SMS to see statistics</p>
                    </div>
                @endif
            </div>

            <!-- Performance Metrics -->
            <div class="chart-container">
                <h3 style="font-size: 1.125rem; font-weight: 600; color: var(--text-primary); margin: 0 0 1.5rem 0; display: flex; align-items: center;">
                    <i class="fas fa-tachometer-alt" style="color: #8b5cf6; margin-right: 0.5rem;"></i>
                    Performance Metrics
                </h3>

                <div style="display: grid; gap: 1rem;">
                    <!-- Delivery Rate -->
                    <div style="padding: 1rem; background: var(--bg-secondary); border-radius: 0.75rem; border: 1px solid var(--border-color);">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                            <span style="font-weight: 500; color: var(--text-primary);">Delivery Rate</span>
                            <span style="font-weight: 700; color: #10b981;">{{ $totalSms > 0 ? number_format(($smsStats['sent'] / $totalSms) * 100, 1) : 0 }}%</span>
                        </div>
                        <div class="progress-bar" style="background: rgba(16, 185, 129, 0.2);">
                            <div style="height: 100%; background: #10b981; border-radius: 4px; width: {{ $totalSms > 0 ? ($smsStats['sent'] / $totalSms) * 100 : 0 }}%; transition: width 0.5s ease;"></div>
                        </div>
                    </div>

                    <!-- Cost Efficiency -->
                    <div style="padding: 1rem; background: var(--bg-secondary); border-radius: 0.75rem; border: 1px solid var(--border-color);">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                            <span style="font-weight: 500; color: var(--text-primary);">Cost Efficiency</span>
                            <span style="font-weight: 700; color: #3b82f6;">৳{{ $totalSms > 0 ? number_format($totalCharge / $totalSms, 2) : 0 }}/SMS</span>
                        </div>
                        @php
                            $efficiency = $totalSms > 0 ? min(($totalCharge / $totalSms / 0.5) * 100, 100) : 0;
                        @endphp
                        <div class="progress-bar" style="background: rgba(59, 130, 246, 0.2);">
                            <div style="height: 100%; background: #3b82f6; border-radius: 4px; width: {{ 100 - $efficiency }}%; transition: width 0.5s ease;"></div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div style="padding: 1rem; background: var(--bg-secondary); border-radius: 0.75rem; border: 1px solid var(--border-color);">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                            <span style="font-weight: 500; color: var(--text-primary);">Activity Score</span>
                            <span style="font-weight: 700; color: #8b5cf6;">{{ min($recentSms->count() * 20, 100) }}%</span>
                        </div>
                        <div class="progress-bar" style="background: rgba(139, 92, 246, 0.2);">
                            <div style="height: 100%; background: #8b5cf6; border-radius: 4px; width: {{ min($recentSms->count() * 20, 100) }}%; transition: width 0.5s ease;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent SMS Table -->
        @if($recentSms->count() > 0)
            <div style="background: var(--card-bg); border-radius: 1rem; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05); border: 1px solid var(--border-color); overflow: hidden; margin-bottom: 2rem;">
                <div style="padding: 1.5rem; border-bottom: 1px solid var(--border-color); background: var(--bg-secondary);">
                    <div style="display: flex; align-items: center; justify-content: space-between; flex-wrap: wrap; gap: 1rem;">
                        <div>
                            <h3 style="font-size: 1.125rem; font-weight: 600; color: var(--text-primary); margin: 0; display: flex; align-items: center;">
                                <i class="fas fa-list" style="color: #3b82f6; margin-right: 0.5rem;"></i>
                                Recent SMS Messages
                            </h3>
                            <p style="font-size: 0.875rem; color: var(--text-secondary); margin: 0.25rem 0 0 0;">Your latest SMS activity</p>
                        </div>
                        <a href="{{ route('client.sms-history') }}" style="background: #3b82f6; color: #ffffff; padding: 0.75rem 1.5rem; border-radius: 0.5rem; font-weight: 600; text-decoration: none; display: flex; align-items: center; transition: background 0.2s ease;" onmouseover="this.style.background='#1d4ed8';" onmouseout="this.style.background='#3b82f6';">
                            <span style="font-size: 0.875rem;">View All</span>
                            <i class="fas fa-arrow-right" style="margin-left: 0.5rem;"></i>
                        </a>
                    </div>
                </div>
                
                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead style="background: var(--bg-secondary);">
                            <tr>
                                <th style="padding: 0.75rem 1.5rem; text-align: left; font-size: 0.75rem; font-weight: 600; color: var(--text-secondary); text-transform: uppercase; letter-spacing: 0.05em;">Recipient</th>
                                <th style="padding: 0.75rem 1.5rem; text-align: left; font-size: 0.75rem; font-weight: 600; color: var(--text-secondary); text-transform: uppercase; letter-spacing: 0.05em;">Message</th>
                                <th style="padding: 0.75rem 1.5rem; text-align: left; font-size: 0.75rem; font-weight: 600; color: var(--text-secondary); text-transform: uppercase; letter-spacing: 0.05em;">Status</th>
                                <th style="padding: 0.75rem 1.5rem; text-align: left; font-size: 0.75rem; font-weight: 600; color: var(--text-secondary); text-transform: uppercase; letter-spacing: 0.05em;">Date</th>
                                <th style="padding: 0.75rem 1.5rem; text-align: left; font-size: 0.75rem; font-weight: 600; color: var(--text-secondary); text-transform: uppercase; letter-spacing: 0.05em;">Charge</th>
                            </tr>
                        </thead>
                        <tbody style="background: var(--card-bg);">
                            @foreach($recentSms as $sms)
                                <tr style="border-bottom: 1px solid var(--border-color); transition: background 0.2s ease;" onmouseover="this.style.background='var(--bg-secondary)';" onmouseout="this.style.background='var(--card-bg)';">
                                    <td style="padding: 1rem 1.5rem;">
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 2.5rem; height: 2.5rem; background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(29, 78, 216, 0.1)); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 1rem;">
                                                <i class="fas fa-mobile-alt" style="color: #3b82f6;"></i>
                                            </div>
                                            <div>
                                                <div style="font-size: 0.875rem; font-weight: 500; color: var(--text-primary);">{{ $sms->to_number }}</div>
                                                <div style="font-size: 0.75rem; color: var(--text-secondary);">Mobile Number</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td style="padding: 1rem 1.5rem;">
                                        <div style="font-size: 0.875rem; color: var(--text-primary); max-width: 20rem;">
                                            <div style="font-weight: 500; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="{{ $sms->msg }}">
                                                {{ Str::limit($sms->msg, 50) }}
                                            </div>
                                            <div style="font-size: 0.75rem; color: var(--text-secondary); margin-top: 0.25rem;">
                                                {{ strlen($sms->msg) }} characters
                                            </div>
                                        </div>
                                    </td>
                                    <td style="padding: 1rem 1.5rem;">
                                        @if($sms->status == 'sent' || $sms->status == '445000')
                                            <span style="display: inline-flex; align-items: center; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500; background: rgba(16, 185, 129, 0.1); color: #059669;">
                                                <i class="fas fa-check" style="margin-right: 0.25rem;"></i>
                                                Delivered
                                            </span>
                                        @elseif($sms->status == 'failed')
                                            <span style="display: inline-flex; align-items: center; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500; background: rgba(239, 68, 68, 0.1); color: #dc2626;">
                                                <i class="fas fa-times" style="margin-right: 0.25rem;"></i>
                                                Failed
                                            </span>
                                        @else
                                            <span style="display: inline-flex; align-items: center; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500; background: rgba(245, 158, 11, 0.1); color: #d97706;">
                                                <i class="fas fa-clock" style="margin-right: 0.25rem;"></i>
                                                Pending
                                            </span>
                                        @endif
                                    </td>
                                    <td style="padding: 1rem 1.5rem; font-size: 0.875rem; color: var(--text-secondary);">
                                        <div style="font-weight: 500; color: var(--text-primary);">{{ $sms->created_at->format('M d, Y') }}</div>
                                        <div style="font-size: 0.75rem; color: var(--text-secondary);">{{ $sms->created_at->format('h:i A') }}</div>
                                    </td>
                                    <td style="padding: 1rem 1.5rem; font-size: 0.875rem; font-weight: 600;">
                                        <div style="color: var(--text-primary); font-weight: 700;">৳{{ number_format($sms->charge, 2) }}</div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        @else
            <div style="background: var(--card-bg); border-radius: 1rem; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05); border: 1px solid var(--border-color); padding: 3rem; text-align: center; margin-bottom: 2rem;">
                <div style="max-width: 20rem; margin: 0 auto;">
                    <div style="background: var(--bg-secondary); border-radius: 50%; width: 5rem; height: 5rem; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem;">
                        <i class="fas fa-inbox" style="font-size: 2rem; color: var(--text-secondary); opacity: 0.5;"></i>
                    </div>
                    <h3 style="font-size: 1.125rem; font-weight: 500; color: var(--text-primary); margin: 0 0 0.5rem 0;">No SMS messages yet</h3>
                    <p style="color: var(--text-secondary); margin: 0 0 1.5rem 0;">Send your first SMS to get started and see your message history here.</p>
                    <a href="#" style="background: #3b82f6; color: #ffffff; padding: 0.75rem 1.5rem; border-radius: 0.5rem; font-weight: 500; text-decoration: none; display: inline-flex; align-items: center; transition: background 0.2s ease;" onmouseover="this.style.background='#1d4ed8';" onmouseout="this.style.background='#3b82f6';">
                        <i class="fas fa-paper-plane" style="margin-right: 0.5rem;"></i>
                        Send First SMS
                    </a>
                </div>
            </div>
        @endif
    </div>

    <!-- Floating Action Button -->
    <div style="position: fixed; bottom: 1.5rem; right: 1.5rem; z-index: 50;">
        <button style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: #ffffff; padding: 1rem; border-radius: 50%; box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3); border: none; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center;" 
                onclick="window.location.href='#'" 
                title="Send Quick SMS"
                onmouseover="this.style.transform='scale(1.1)'; this.style.boxShadow='0 15px 35px rgba(59, 130, 246, 0.4)';" 
                onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 10px 25px rgba(59, 130, 246, 0.3)';">
            <i class="fas fa-paper-plane" style="font-size: 1.25rem;"></i>
        </button>
    </div>

    <script>
        // Dashboard real-time updates
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth transitions to progress bars
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 500);
            });

            // Auto-refresh functionality
            let refreshInterval;
            
            function startAutoRefresh() {
                refreshInterval = setInterval(async () => {
                    try {
                        // Here you would typically fetch new data from the API
                        // For now, we'll just simulate the refresh
                        console.log('Dashboard refreshed');
                    } catch (error) {
                        console.error('Failed to refresh dashboard:', error);
                    }
                }, 30000); // Refresh every 30 seconds
            }

            // Start auto-refresh
            startAutoRefresh();

            // Pause refresh when page is not visible
            document.addEventListener('visibilitychange', function() {
                if (document.hidden) {
                    clearInterval(refreshInterval);
                } else {
                    startAutoRefresh();
                }
            });
        });
    </script>
</x-client-layout>
