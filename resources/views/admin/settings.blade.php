@extends('admin.layouts.app')

@section('title', 'Settings')
@section('page-title', 'Settings')

@section('content')
<!-- Settings Header -->
<div class="bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-700 rounded-2xl p-8 mb-8 text-white shadow-2xl border border-indigo-200 relative overflow-hidden">
    <!-- Background decoration -->
    <div class="absolute top-0 right-0 w-32 h-32 bg-white bg-opacity-10 rounded-full -mr-16 -mt-16"></div>
    <div class="absolute bottom-0 left-0 w-24 h-24 bg-white bg-opacity-10 rounded-full -ml-12 -mb-12"></div>
    
    <div class="relative z-10">
        <div class="flex flex-col md:flex-row items-start md:items-center justify-between">
            <div class="mb-4 md:mb-0">
                <h1 class="text-3xl md:text-4xl font-bold mb-3 text-white">
                    System Settings ⚙️
                </h1>
                <p class="text-indigo-100 text-lg">
                    Configure your SMS City admin panel preferences and system settings
                </p>
            </div>
            <div class="text-right">
                <div class="bg-white bg-opacity-15 rounded-2xl p-6 backdrop-blur-sm border border-white/20">
                    <div class="text-2xl font-bold text-white mb-1">{{ now()->format('H:i') }}</div>
                    <div class="text-indigo-100 text-sm">{{ now()->format('M d, Y') }}</div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="space-y-8">
    <!-- Quick Settings Overview -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-all duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-2">App Status</p>
                    <p class="text-2xl font-bold text-green-600">Online</p>
                </div>
                <div class="bg-green-100 p-3 rounded-xl">
                    <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-all duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-2">Version</p>
                    <p class="text-2xl font-bold text-blue-600">v2.1.0</p>
                </div>
                <div class="bg-blue-100 p-3 rounded-xl">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-all duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-2">Environment</p>
                    <p class="text-2xl font-bold text-purple-600">{{ config('app.env') }}</p>
                </div>
                <div class="bg-purple-100 p-3 rounded-xl">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-all duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-2">Debug Mode</p>
                    <p class="text-2xl font-bold {{ $appSettings['debug_mode'] ? 'text-yellow-600' : 'text-green-600' }}">
                        {{ $appSettings['debug_mode'] ? 'ON' : 'OFF' }}
                    </p>
                </div>
                <div class="bg-{{ $appSettings['debug_mode'] ? 'yellow' : 'green' }}-100 p-3 rounded-xl">
                    <svg class="w-6 h-6 text-{{ $appSettings['debug_mode'] ? 'yellow' : 'green' }}-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Tabs -->
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100" x-data="{ activeTab: 'general' }">
        <!-- Tab Navigation -->
        <div class="border-b border-gray-200 rounded-t-2xl bg-gray-50">
            <nav class="flex space-x-8 px-6" aria-label="Tabs">
                <button @click="activeTab = 'general'"
                        :class="activeTab === 'general' ? 'border-indigo-500 text-indigo-600 bg-white' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                        class="whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm rounded-t-lg transition-all duration-200">
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span>General</span>
                    </div>
                </button>
                
                <button @click="activeTab = 'sms'"
                        :class="activeTab === 'sms' ? 'border-indigo-500 text-indigo-600 bg-white' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                        class="whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm rounded-t-lg transition-all duration-200">
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        <span>SMS</span>
                    </div>
                </button>
                
                <button @click="activeTab = 'notifications'"
                        :class="activeTab === 'notifications' ? 'border-indigo-500 text-indigo-600 bg-white' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                        class="whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm rounded-t-lg transition-all duration-200">
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.5a6 6 0 0 1 6 6v2l1.5 3h-15l1.5-3v-2a6 6 0 0 1 6-6z"></path>
                        </svg>
                        <span>Notifications</span>
                    </div>
                </button>
                
                <button @click="activeTab = 'security'"
                        :class="activeTab === 'security' ? 'border-indigo-500 text-indigo-600 bg-white' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                        class="whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm rounded-t-lg transition-all duration-200">
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                        <span>Security</span>
                    </div>
                </button>
            </nav>
        </div>

        <!-- Tab Content -->
        <div class="p-8">
            <!-- General Settings -->
            <div x-show="activeTab === 'general'" class="space-y-6">
                <div>
                    <h3 class="text-xl font-bold text-gray-900 mb-6">General Application Settings</h3>
                    <form method="POST" action="{{ route('admin.settings.update') }}" class="space-y-6">
                        @csrf
                        @method('PATCH')
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="app_name" class="block text-sm font-semibold text-gray-700 mb-2">Application Name</label>
                                <input type="text" id="app_name" name="app_name" value="{{ $appSettings['app_name'] }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200">
                            </div>
                            
                            <div>
                                <label for="timezone" class="block text-sm font-semibold text-gray-700 mb-2">Timezone</label>
                                <select id="timezone" name="timezone"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200">
                                    <option value="UTC" {{ $appSettings['timezone'] === 'UTC' ? 'selected' : '' }}>UTC</option>
                                    <option value="America/New_York" {{ $appSettings['timezone'] === 'America/New_York' ? 'selected' : '' }}>Eastern Time</option>
                                    <option value="America/Chicago" {{ $appSettings['timezone'] === 'America/Chicago' ? 'selected' : '' }}>Central Time</option>
                                    <option value="America/Denver" {{ $appSettings['timezone'] === 'America/Denver' ? 'selected' : '' }}>Mountain Time</option>
                                    <option value="America/Los_Angeles" {{ $appSettings['timezone'] === 'America/Los_Angeles' ? 'selected' : '' }}>Pacific Time</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="flex justify-end">
                            <button type="submit"
                                    class="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl">
                                Save General Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- SMS Settings -->
            <div x-show="activeTab === 'sms'" class="space-y-6">
                <div>
                    <h3 class="text-xl font-bold text-gray-900 mb-6">SMS Configuration</h3>
                    <form method="POST" action="{{ route('admin.settings.update') }}" class="space-y-6">
                        @csrf
                        @method('PATCH')
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="max_message_length" class="block text-sm font-semibold text-gray-700 mb-2">Max Message Length</label>
                                <input type="number" id="max_message_length" name="max_message_length" value="{{ $smsSettings['max_message_length'] }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200">
                            </div>
                            
                            <div>
                                <label for="rate_limit" class="block text-sm font-semibold text-gray-700 mb-2">Rate Limit (per hour)</label>
                                <input type="number" id="rate_limit" name="rate_limit" value="{{ $smsSettings['rate_limit'] }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200">
                            </div>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="auto_retry" name="auto_retry" value="1" {{ $smsSettings['auto_retry'] ? 'checked' : '' }}
                                   class="w-5 h-5 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500 mr-3">
                            <label for="auto_retry" class="text-sm font-medium text-gray-700">Enable auto-retry for failed messages</label>
                        </div>
                        
                        <div class="flex justify-end">
                            <button type="submit"
                                    class="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl">
                                Save SMS Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Notification Settings -->
            <div x-show="activeTab === 'notifications'" class="space-y-6">
                <div>
                    <h3 class="text-xl font-bold text-gray-900 mb-6">Notification Preferences</h3>
                    <form method="POST" action="{{ route('admin.settings.update') }}" class="space-y-6">
                        @csrf
                        @method('PATCH')
                        
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                                <div>
                                    <h4 class="text-sm font-semibold text-gray-900">Email Notifications</h4>
                                    <p class="text-xs text-gray-600">Receive notifications via email</p>
                                </div>
                                <input type="checkbox" name="email_notifications" value="1" {{ $notificationSettings['email_notifications'] ? 'checked' : '' }}
                                       class="w-5 h-5 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                            </div>
                            
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                                <div>
                                    <h4 class="text-sm font-semibold text-gray-900">SMS Notifications</h4>
                                    <p class="text-xs text-gray-600">Receive notifications via SMS</p>
                                </div>
                                <input type="checkbox" name="sms_notifications" value="1" {{ $notificationSettings['sms_notifications'] ? 'checked' : '' }}
                                       class="w-5 h-5 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                            </div>
                            
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                                <div>
                                    <h4 class="text-sm font-semibold text-gray-900">Browser Notifications</h4>
                                    <p class="text-xs text-gray-600">Receive push notifications in browser</p>
                                </div>
                                <input type="checkbox" name="browser_notifications" value="1" {{ $notificationSettings['browser_notifications'] ? 'checked' : '' }}
                                       class="w-5 h-5 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                            </div>
                        </div>
                        
                        <div class="flex justify-end">
                            <button type="submit"
                                    class="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl">
                                Save Notification Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Security Settings -->
            <div x-show="activeTab === 'security'" class="space-y-6">
                <div>
                    <h3 class="text-xl font-bold text-gray-900 mb-6">Security Configuration</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-xl border border-green-200">
                            <div class="flex items-center mb-4">
                                <div class="bg-green-500 p-2 rounded-lg mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <h4 class="text-lg font-semibold text-green-900">SSL/HTTPS</h4>
                            </div>
                            <p class="text-sm text-green-700 mb-4">Your connection is secure and encrypted.</p>
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                                <span class="text-sm font-medium text-green-800">Active</span>
                            </div>
                        </div>
                        
                        <div class="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-200">
                            <div class="flex items-center mb-4">
                                <div class="bg-blue-500 p-2 rounded-lg mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                    </svg>
                                </div>
                                <h4 class="text-lg font-semibold text-blue-900">Session Security</h4>
                            </div>
                            <p class="text-sm text-blue-700 mb-4">Sessions are automatically secured.</p>
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
                                <span class="text-sm font-medium text-blue-800">Protected</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
                        <div class="flex items-center mb-4">
                            <svg class="w-6 h-6 text-yellow-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <h4 class="text-lg font-semibold text-yellow-800">Security Recommendations</h4>
                        </div>
                        <ul class="space-y-2 text-sm text-yellow-700">
                            <li class="flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                </svg>
                                Change your password regularly
                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                </svg>
                                Keep your system updated
                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                </svg>
                                Monitor user activity regularly
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Information -->
    <div class="bg-white rounded-2xl shadow-lg p-8 border border-gray-100">
        <h3 class="text-xl font-bold text-gray-900 mb-6">System Information</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-gray-50 p-4 rounded-xl">
                <h4 class="text-sm font-semibold text-gray-700 mb-2">PHP Version</h4>
                <p class="text-lg font-bold text-gray-900">{{ phpversion() }}</p>
            </div>
            
            <div class="bg-gray-50 p-4 rounded-xl">
                <h4 class="text-sm font-semibold text-gray-700 mb-2">Laravel Version</h4>
                <p class="text-lg font-bold text-gray-900">{{ app()->version() }}</p>
            </div>
            
            <div class="bg-gray-50 p-4 rounded-xl">
                <h4 class="text-sm font-semibold text-gray-700 mb-2">Server Time</h4>
                <p class="text-lg font-bold text-gray-900">{{ now()->format('H:i:s T') }}</p>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-update server time
setInterval(function() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {
        hour12: false,
        timeZone: '{{ $appSettings["timezone"] }}'
    });
    const timeElements = document.querySelectorAll('[data-time]');
    timeElements.forEach(el => el.textContent = timeString);
}, 1000);
</script>
@endsection
