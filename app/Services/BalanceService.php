<?php

namespace App\Services;

use App\Models\Client;
use App\Models\SMS;
use App\Models\BalanceTransaction;
use Illuminate\Support\Facades\DB;

class BalanceService
{
    /**
     * Calculate SMS cost based on message length.
     */
    public function calculateSMSCost(string $message, float $baseRate = 0.25): float
    {
        $messageLength = strlen($message);
        
        // Standard SMS is 160 characters
        // Longer messages are charged as multiple SMS
        $smsCount = ceil($messageLength / 160);
        
        return $smsCount * $baseRate;
    }

    /**
     * Process SMS with balance deduction.
     */
    public function processSMSWithBalance(Client $client, array $smsData): array
    {
        return DB::transaction(function () use ($client, $smsData) {
            // Calculate cost
            $cost = $this->calculateSMSCost($smsData['msg']);
            
            // Check if client has sufficient balance
            if (!$client->hasSufficientBalance($cost)) {
                throw new \Exception(
                    "Insufficient balance. Required: ৳" . number_format($cost, 2) . 
                    ", Available: ৳" . number_format($client->balance, 2)
                );
            }

            // Create SMS record
            $sms = $client->sms()->create([
                'to_number' => $smsData['to_number'],
                'msg' => $smsData['msg'],
                'charge' => $cost,
                'status' => 'pending',
            ]);

            // Deduct balance
            $transaction = $client->deductBalance(
                amount: $cost,
                description: "SMS to " . $smsData['to_number'],
                reference: $sms->id,
                source: 'sms'
            );

            return [
                'sms' => $sms,
                'transaction' => $transaction,
                'remaining_balance' => $client->fresh()->balance
            ];
        });
    }

    /**
     * Refund SMS cost if SMS failed.
     */
    public function refundFailedSMS(SMS $sms): ?BalanceTransaction
    {
        if ($sms->status !== 'failed' || $sms->charge <= 0) {
            return null;
        }

        // Check if already refunded
        $existingRefund = BalanceTransaction::where('reference', $sms->id)
            ->where('type', 'credit')
            ->where('source', 'sms')
            ->where('description', 'like', '%refund%')
            ->first();

        if ($existingRefund) {
            return null; // Already refunded
        }

        return $sms->client->addBalance(
            amount: $sms->charge,
            description: "SMS refund for failed message to " . $sms->to_number,
            reference: $sms->id,
            source: 'sms'
        );
    }

    /**
     * Get client balance summary.
     */
    public function getClientBalanceSummary(Client $client): array
    {
        $transactions = $client->balanceTransactions();
        
        return [
            'current_balance' => $client->balance,
            'formatted_balance' => $client->formatted_balance,
            'total_credited' => $transactions->credit()->sum('amount'),
            'total_debited' => $transactions->debit()->sum('amount'),
            'sms_spent' => $transactions->bySource('sms')->debit()->sum('amount'),
            'admin_additions' => $transactions->bySource('admin')->credit()->sum('amount'),
            'last_transaction' => $transactions->latest()->first(),
            'low_balance_threshold' => 10.00,
            'is_low_balance' => $client->balance < 10.00,
            'estimated_sms_count' => floor($client->balance / 0.25), // Assuming 0.25 per SMS
        ];
    }

    /**
     * Get balance analytics for admin dashboard.
     */
    public function getBalanceAnalytics(): array
    {
        return [
            'total_clients' => Client::count(),
            'total_balance' => Client::sum('balance'),
            'average_balance' => Client::avg('balance'),
            'low_balance_clients' => Client::where('balance', '<', 10)->count(),
            'zero_balance_clients' => Client::where('balance', '<=', 0)->count(),
            'top_balance_clients' => Client::orderBy('balance', 'desc')->limit(5)->get(['name', 'balance']),
            'recent_transactions' => BalanceTransaction::with('client')
                ->latest()
                ->limit(10)
                ->get(),
            'daily_transactions' => BalanceTransaction::whereDate('created_at', today())
                ->selectRaw('type, SUM(amount) as total, COUNT(*) as count')
                ->groupBy('type')
                ->get(),
            'monthly_transactions' => BalanceTransaction::whereMonth('created_at', now()->month)
                ->selectRaw('type, SUM(amount) as total, COUNT(*) as count')
                ->groupBy('type')
                ->get(),
        ];
    }
}
