<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\SMS;
use App\Services\BalanceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;


class SMSController extends Controller
{
    protected $balanceService;

    public function __construct(BalanceService $balanceService)
    {
        $this->balanceService = $balanceService;
    }

public function send_sms(Request $request)
{
    try {
        $apiKey = $request->header('X-API-KEY');
        $clientInfo = Client::where('authorization', $apiKey)->first();

        if (!$apiKey || !$clientInfo) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $validator = Validator::make($request->all(), [
            'msg' => 'required|string',
            'contacts' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Calculate SMS cost
        $smsCost = $this->balanceService->calculateSMSCost($request->msg);
        
        // Check if client has sufficient balance
        if (!$clientInfo->hasSufficientBalance($smsCost)) {
            return response()->json([
                'error' => 'Insufficient balance',
                'required' => $smsCost,
                'available' => $clientInfo->balance,
                'message' => 'Please add balance to your account to send SMS.'
            ], 402);
        }

        $smsApiKey = '4451551156210561551156210';
        $sender = '***********';

        $url = "http://sms.iglweb.com/api/v1/send";

        // Prepare payload
        $payload = [
            'api_key' => $smsApiKey,
            'contacts' => $request->contacts,
            'senderid' => $sender,
            'msg' => $request->msg,
        ];

        // Initialize cURL session
$ch = curl_init();

curl_setopt_array($ch, [
    CURLOPT_URL => $url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_CUSTOMREQUEST => "GET",
    CURLOPT_HTTPHEADER => [
        "Content-Type: application/json",
    ],
    CURLOPT_POSTFIELDS => json_encode($payload),
    CURLOPT_TIMEOUT => 30,
    CURLOPT_IPRESOLVE => CURL_IPRESOLVE_V4,
    CURLOPT_SSL_VERIFYPEER => false, // Only for testing, remove in production
    CURLOPT_VERBOSE => true, // For detailed logging
]);

// Execute cURL request
$response = curl_exec($ch);

// Handle cURL error
if ($response === false) {
    $errorCode = curl_errno($ch);
    $errorMessage = curl_error($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    curl_close($ch);

    return response()->json([
        'error' => 'SMS request failed',
        'curl_errno' => $errorCode,
        'curl_error' => $errorMessage,
        'http_code' => $httpCode,
        'details' => [
            'url' => $url,
            'payload' => $payload,
        ],
    ], 500);
}

// Get HTTP status code even if request didn't fail
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

// Close cURL session
curl_close($ch);

// You might also want to check for HTTP errors
if ($httpCode >= 400) {
    return response()->json([
        'error' => 'SMS API returned error',
        'http_code' => $httpCode,
        'api_response' => $response,
    ], 500);
}

        // Decode JSON response
        $data = json_decode($response, true);

        // Determine if SMS was successful
        $isSuccessful = isset($data['code']) && in_array($data['code'], [200, 202, 'success']);
        $smsStatus = $isSuccessful ? 'sent' : 'failed';

        try {
            // Deduct balance if SMS was successful
            if ($isSuccessful) {
                $clientInfo->deductBalance(
                    $smsCost,
                    "SMS sent to {$request->contacts}",
                    null,
                    'sms'
                );
            }

            // Insert SMS log
            SMS::insert([
                'client_id' => $clientInfo->id,
                'from_number' => $sender,
                'to_number' => $request->contacts,
                'msg' => $request->msg,
                'charge' => $smsCost,
                'status' => $smsStatus,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

        } catch (\Exception $balanceException) {
            // Log balance deduction error but don't fail the SMS response
            Log::error('Balance deduction failed for SMS', [
                'client_id' => $clientInfo->id,
                'amount' => $smsCost,
                'error' => $balanceException->getMessage()
            ]);
        }

        return response()->json([
            'message' => $data['message'] ?? 'Message sent',
            'code' => $data['code'] ?? 200,
            'sms_cost' => $smsCost,
            'remaining_balance' => $clientInfo->fresh()->balance,
            'balance_deducted' => $isSuccessful
        ], 200);

    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Unexpected error',
            'message' => $e->getMessage(),
            'line' => $e->getLine(),
        ], 500);
    }
}




}
