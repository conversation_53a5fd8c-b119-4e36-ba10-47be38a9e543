<x-admin-layout>
    <x-slot name="header">
        Client Balance Management
    </x-slot>

    <style>
        .balance-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 1rem;
            padding: 1.5rem;
            color: white;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .stat-card {
            background: white;
            border-radius: 0.75rem;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .low-balance {
            background: #fef2f2;
            border-left: 4px solid #ef4444;
        }

        .high-balance {
            background: #f0fdf4;
            border-left: 4px solid #22c55e;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 0.5rem;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }
    </style>

    <!-- Summary Cards -->
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
        <div class="stat-card">
            <div style="display: flex; align-items: center; justify-content: between;">
                <div>
                    <h3 style="font-size: 0.875rem; font-weight: 600; color: #6b7280; text-transform: uppercase; margin: 0;">Total Clients</h3>
                    <p style="font-size: 2rem; font-weight: 700; color: #111827; margin: 0.5rem 0;">{{ number_format($totalClients) }}</p>
                </div>
                <div style="background: #3b82f6; padding: 0.75rem; border-radius: 0.5rem;">
                    <i class="fas fa-users" style="color: white; font-size: 1.5rem;"></i>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div style="display: flex; align-items: center; justify-content: between;">
                <div>
                    <h3 style="font-size: 0.875rem; font-weight: 600; color: #6b7280; text-transform: uppercase; margin: 0;">Total Balance</h3>
                    <p style="font-size: 2rem; font-weight: 700; color: #111827; margin: 0.5rem 0;">৳{{ number_format($totalBalance, 2) }}</p>
                </div>
                <div style="background: #10b981; padding: 0.75rem; border-radius: 0.5rem;">
                    <i class="fas fa-wallet" style="color: white; font-size: 1.5rem;"></i>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div style="display: flex; align-items: center; justify-content: between;">
                <div>
                    <h3 style="font-size: 0.875rem; font-weight: 600; color: #6b7280; text-transform: uppercase; margin: 0;">Low Balance</h3>
                    <p style="font-size: 2rem; font-weight: 700; color: #111827; margin: 0.5rem 0;">{{ number_format($lowBalanceClients) }}</p>
                </div>
                <div style="background: #ef4444; padding: 0.75rem; border-radius: 0.5rem;">
                    <i class="fas fa-exclamation-triangle" style="color: white; font-size: 1.5rem;"></i>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div style="display: flex; align-items: center; justify-content: between;">
                <div>
                    <h3 style="font-size: 0.875rem; font-weight: 600; color: #6b7280; text-transform: uppercase; margin: 0;">Avg Balance</h3>
                    <p style="font-size: 2rem; font-weight: 700; color: #111827; margin: 0.5rem 0;">৳{{ number_format($totalClients > 0 ? $totalBalance / $totalClients : 0, 2) }}</p>
                </div>
                <div style="background: #8b5cf6; padding: 0.75rem; border-radius: 0.5rem;">
                    <i class="fas fa-chart-bar" style="color: white; font-size: 1.5rem;"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div style="background: white; padding: 1.5rem; border-radius: 0.75rem; margin-bottom: 2rem; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
        <form method="GET" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; align-items: end;">
            <div>
                <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Search</label>
                <input type="text" name="search" value="{{ request('search') }}" placeholder="Name, email, or authorization..."
                       style="width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem;">
            </div>
            
            <div>
                <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Status</label>
                <select name="status" style="width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem;">
                    <option value="">All Status</option>
                    <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>Pending</option>
                    <option value="2" {{ request('status') == '2' ? 'selected' : '' }}>Active</option>
                    <option value="3" {{ request('status') == '3' ? 'selected' : '' }}>Deactivated</option>
                </select>
            </div>

            <div>
                <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Min Balance</label>
                <input type="number" name="min_balance" value="{{ request('min_balance') }}" step="0.01" min="0"
                       style="width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem;">
            </div>

            <div>
                <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Max Balance</label>
                <input type="number" name="max_balance" value="{{ request('max_balance') }}" step="0.01" min="0"
                       style="width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem;">
            </div>

            <div style="display: flex; gap: 0.5rem;">
                <button type="submit" style="background: #3b82f6; color: white; padding: 0.5rem 1rem; border: none; border-radius: 0.375rem; font-size: 0.875rem; font-weight: 500; cursor: pointer; transition: background 0.2s;">
                    <i class="fas fa-search"></i> Filter
                </button>
                <a href="{{ route('admin.client-balance.index') }}" style="background: #6b7280; color: white; padding: 0.5rem 1rem; border-radius: 0.375rem; font-size: 0.875rem; font-weight: 500; text-decoration: none; display: inline-flex; align-items: center;">
                    <i class="fas fa-times"></i> Clear
                </a>
            </div>
        </form>
    </div>

    <!-- Bulk Actions -->
    <div style="background: white; padding: 1rem; border-radius: 0.75rem; margin-bottom: 1rem; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
        <div style="display: flex; align-items: center; justify-content: between; flex-wrap: wrap; gap: 1rem;">
            <div style="display: flex; align-items: center; gap: 1rem;">
                <label style="display: flex; align-items: center; gap: 0.5rem; font-weight: 500;">
                    <input type="checkbox" id="selectAll" style="width: 1rem; height: 1rem;">
                    Select All
                </label>
                <span id="selectedCount" style="color: #6b7280; font-size: 0.875rem;">0 selected</span>
            </div>
            <button id="bulkAddBalanceBtn" style="background: #10b981; color: white; padding: 0.5rem 1rem; border: none; border-radius: 0.375rem; font-size: 0.875rem; font-weight: 500; cursor: pointer; opacity: 0.5;" disabled>
                <i class="fas fa-plus"></i> Bulk Add Balance
            </button>
        </div>
    </div>

    <!-- Clients Table -->
    <div style="background: white; border-radius: 0.75rem; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); overflow: hidden;">
        <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse;">
                <thead style="background: #f9fafb;">
                    <tr>
                        <th style="padding: 0.75rem; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">
                            <input type="checkbox" id="selectAllHeader" style="width: 1rem; height: 1rem;">
                        </th>
                        <th style="padding: 0.75rem; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Client</th>
                        <th style="padding: 0.75rem; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Balance</th>
                        <th style="padding: 0.75rem; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Status</th>
                        <th style="padding: 0.75rem; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Recent Transactions</th>
                        <th style="padding: 0.75rem; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($clients as $client)
                    <tr style="border-bottom: 1px solid #e5e7eb;" class="{{ $client->balance < 10 ? 'low-balance' : ($client->balance > 100 ? 'high-balance' : '') }}">
                        <td style="padding: 1rem;">
                            <input type="checkbox" name="client_ids[]" value="{{ $client->id }}" class="client-checkbox" style="width: 1rem; height: 1rem;">
                        </td>
                        <td style="padding: 1rem;">
                            <div>
                                <div style="font-weight: 600; color: #111827;">{{ $client->name }}</div>
                                @if($client->email)
                                <div style="font-size: 0.875rem; color: #6b7280;">{{ $client->email }}</div>
                                @endif
                                <div style="font-size: 0.75rem; color: #9ca3af;">ID: {{ $client->authorization }}</div>
                            </div>
                        </td>
                        <td style="padding: 1rem;">
                            <div style="font-size: 1.25rem; font-weight: 700; color: {{ $client->balance < 10 ? '#ef4444' : ($client->balance > 100 ? '#10b981' : '#111827') }};">
                                ৳{{ number_format($client->balance, 2) }}
                            </div>
                            @if($client->balance < 10)
                            <div style="font-size: 0.75rem; color: #ef4444; font-weight: 500;">Low Balance</div>
                            @endif
                        </td>
                        <td style="padding: 1rem;">
                            @if($client->status == '1')
                            <span style="background: #fef3c7; color: #92400e; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500;">Pending</span>
                            @elseif($client->status == '2')
                            <span style="background: #dcfce7; color: #166534; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500;">Active</span>
                            @else
                            <span style="background: #fee2e2; color: #991b1b; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500;">Deactivated</span>
                            @endif
                        </td>
                        <td style="padding: 1rem;">
                            @if($client->balanceTransactions->count() > 0)
                            <div style="font-size: 0.875rem;">
                                @foreach($client->balanceTransactions->take(2) as $transaction)
                                <div style="margin-bottom: 0.25rem;">
                                    <span style="color: {{ $transaction->type == 'credit' ? '#10b981' : '#ef4444' }}; font-weight: 500;">
                                        {{ $transaction->type == 'credit' ? '+' : '-' }}৳{{ number_format($transaction->amount, 2) }}
                                    </span>
                                    <span style="color: #6b7280; font-size: 0.75rem;">{{ $transaction->created_at->diffForHumans() }}</span>
                                </div>
                                @endforeach
                            </div>
                            @else
                            <span style="color: #9ca3af; font-size: 0.875rem;">No transactions</span>
                            @endif
                        </td>
                        <td style="padding: 1rem;">
                            <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                <button onclick="openAddBalanceModal({{ $client->id }}, '{{ $client->name }}')" 
                                        style="background: #10b981; color: white; padding: 0.375rem 0.75rem; border: none; border-radius: 0.375rem; font-size: 0.75rem; cursor: pointer; transition: background 0.2s;">
                                    <i class="fas fa-plus"></i> Add
                                </button>
                                <button onclick="openDeductBalanceModal({{ $client->id }}, '{{ $client->name }}', {{ $client->balance }})" 
                                        style="background: #ef4444; color: white; padding: 0.375rem 0.75rem; border: none; border-radius: 0.375rem; font-size: 0.75rem; cursor: pointer; transition: background 0.2s;">
                                    <i class="fas fa-minus"></i> Deduct
                                </button>
                                <a href="{{ route('admin.client-balance.show', $client) }}" 
                                   style="background: #3b82f6; color: white; padding: 0.375rem 0.75rem; border-radius: 0.375rem; font-size: 0.75rem; text-decoration: none; transition: background 0.2s;">
                                    <i class="fas fa-eye"></i> View
                                </a>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="6" style="padding: 3rem; text-align: center; color: #6b7280;">
                            <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                            <div style="font-size: 1.125rem; font-weight: 500; margin-bottom: 0.5rem;">No clients found</div>
                            <div style="font-size: 0.875rem;">Try adjusting your search criteria</div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if($clients->hasPages())
        <div style="padding: 1rem; border-top: 1px solid #e5e7eb; background: #f9fafb;">
            {{ $clients->links() }}
        </div>
        @endif
    </div>

    <!-- Add Balance Modal -->
    <div id="addBalanceModal" class="modal">
        <div class="modal-content">
            <h2 style="font-size: 1.5rem; font-weight: 700; margin-bottom: 1rem;">Add Balance</h2>
            <form id="addBalanceForm">
                @csrf
                <input type="hidden" id="addBalanceClientId">
                
                <div style="margin-bottom: 1rem;">
                    <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Client</label>
                    <div id="addBalanceClientName" style="font-weight: 600; color: #111827;"></div>
                </div>

                <div style="margin-bottom: 1rem;">
                    <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Amount (৳)</label>
                    <input type="number" id="addAmount" step="0.01" min="0.01" max="99999.99" required
                           style="width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem;">
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Description (Optional)</label>
                    <textarea id="addDescription" rows="3" placeholder="Reason for adding balance..."
                              style="width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; resize: vertical;"></textarea>
                </div>

                <div style="display: flex; gap: 0.5rem; justify-content: end;">
                    <button type="button" onclick="closeModal('addBalanceModal')" 
                            style="background: #6b7280; color: white; padding: 0.5rem 1rem; border: none; border-radius: 0.375rem; cursor: pointer;">
                        Cancel
                    </button>
                    <button type="submit" 
                            style="background: #10b981; color: white; padding: 0.5rem 1rem; border: none; border-radius: 0.375rem; cursor: pointer; font-weight: 500;">
                        Add Balance
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Deduct Balance Modal -->
    <div id="deductBalanceModal" class="modal">
        <div class="modal-content">
            <h2 style="font-size: 1.5rem; font-weight: 700; margin-bottom: 1rem;">Deduct Balance</h2>
            <form id="deductBalanceForm">
                @csrf
                <input type="hidden" id="deductBalanceClientId">
                
                <div style="margin-bottom: 1rem;">
                    <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Client</label>
                    <div id="deductBalanceClientName" style="font-weight: 600; color: #111827;"></div>
                </div>

                <div style="margin-bottom: 1rem;">
                    <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Current Balance</label>
                    <div id="currentBalance" style="font-size: 1.25rem; font-weight: 700; color: #10b981;"></div>
                </div>

                <div style="margin-bottom: 1rem;">
                    <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Amount to Deduct (৳)</label>
                    <input type="number" id="deductAmount" step="0.01" min="0.01" required
                           style="width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem;">
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Description (Optional)</label>
                    <textarea id="deductDescription" rows="3" placeholder="Reason for deducting balance..."
                              style="width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; resize: vertical;"></textarea>
                </div>

                <div style="display: flex; gap: 0.5rem; justify-content: end;">
                    <button type="button" onclick="closeModal('deductBalanceModal')" 
                            style="background: #6b7280; color: white; padding: 0.5rem 1rem; border: none; border-radius: 0.375rem; cursor: pointer;">
                        Cancel
                    </button>
                    <button type="submit" 
                            style="background: #ef4444; color: white; padding: 0.5rem 1rem; border: none; border-radius: 0.375rem; cursor: pointer; font-weight: 500;">
                        Deduct Balance
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Bulk Add Balance Modal -->
    <div id="bulkAddBalanceModal" class="modal">
        <div class="modal-content">
            <h2 style="font-size: 1.5rem; font-weight: 700; margin-bottom: 1rem;">Bulk Add Balance</h2>
            <form id="bulkAddBalanceForm">
                @csrf
                
                <div style="margin-bottom: 1rem;">
                    <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Selected Clients</label>
                    <div id="bulkSelectedClients" style="font-weight: 600; color: #111827;"></div>
                </div>

                <div style="margin-bottom: 1rem;">
                    <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Amount per Client (৳)</label>
                    <input type="number" id="bulkAmount" step="0.01" min="0.01" max="99999.99" required
                           style="width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem;">
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Description (Optional)</label>
                    <textarea id="bulkDescription" rows="3" placeholder="Reason for adding balance..."
                              style="width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; resize: vertical;"></textarea>
                </div>

                <div style="display: flex; gap: 0.5rem; justify-content: end;">
                    <button type="button" onclick="closeModal('bulkAddBalanceModal')" 
                            style="background: #6b7280; color: white; padding: 0.5rem 1rem; border: none; border-radius: 0.375rem; cursor: pointer;">
                        Cancel
                    </button>
                    <button type="submit" 
                            style="background: #10b981; color: white; padding: 0.5rem 1rem; border: none; border-radius: 0.375rem; cursor: pointer; font-weight: 500;">
                        Add Balance
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Modal functions
        function openAddBalanceModal(clientId, clientName) {
            document.getElementById('addBalanceClientId').value = clientId;
            document.getElementById('addBalanceClientName').textContent = clientName;
            document.getElementById('addBalanceModal').classList.add('show');
        }

        function openDeductBalanceModal(clientId, clientName, currentBalance) {
            document.getElementById('deductBalanceClientId').value = clientId;
            document.getElementById('deductBalanceClientName').textContent = clientName;
            document.getElementById('currentBalance').textContent = '৳' + parseFloat(currentBalance).toFixed(2);
            document.getElementById('deductAmount').max = currentBalance;
            document.getElementById('deductBalanceModal').classList.add('show');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }

        // Checkbox handling
        const selectAllCheckboxes = document.querySelectorAll('#selectAll, #selectAllHeader');
        const clientCheckboxes = document.querySelectorAll('.client-checkbox');
        const selectedCountSpan = document.getElementById('selectedCount');
        const bulkAddBalanceBtn = document.getElementById('bulkAddBalanceBtn');

        function updateSelectedCount() {
            const selectedCheckboxes = document.querySelectorAll('.client-checkbox:checked');
            const count = selectedCheckboxes.length;
            
            selectedCountSpan.textContent = count + ' selected';
            bulkAddBalanceBtn.disabled = count === 0;
            bulkAddBalanceBtn.style.opacity = count === 0 ? '0.5' : '1';

            // Update select all checkboxes
            const allSelected = count === clientCheckboxes.length && count > 0;
            selectAllCheckboxes.forEach(checkbox => {
                checkbox.checked = allSelected;
                checkbox.indeterminate = count > 0 && count < clientCheckboxes.length;
            });
        }

        selectAllCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                clientCheckboxes.forEach(cb => cb.checked = this.checked);
                updateSelectedCount();
            });
        });

        clientCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedCount);
        });

        // Bulk add balance
        bulkAddBalanceBtn.addEventListener('click', function() {
            const selectedCheckboxes = document.querySelectorAll('.client-checkbox:checked');
            if (selectedCheckboxes.length === 0) return;

            document.getElementById('bulkSelectedClients').textContent = selectedCheckboxes.length + ' clients selected';
            document.getElementById('bulkAddBalanceModal').classList.add('show');
        });

        // Form submissions
        document.getElementById('addBalanceForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const clientId = document.getElementById('addBalanceClientId').value;
            const amount = document.getElementById('addAmount').value;
            const description = document.getElementById('addDescription').value;

            fetch(`/admin/client-balance/${clientId}/add-balance`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({ amount, description })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                alert('An error occurred: ' + error.message);
            });
        });

        document.getElementById('deductBalanceForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const clientId = document.getElementById('deductBalanceClientId').value;
            const amount = document.getElementById('deductAmount').value;
            const description = document.getElementById('deductDescription').value;

            fetch(`/admin/client-balance/${clientId}/deduct-balance`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({ amount, description })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                alert('An error occurred: ' + error.message);
            });
        });

        document.getElementById('bulkAddBalanceForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const selectedCheckboxes = document.querySelectorAll('.client-checkbox:checked');
            const clientIds = Array.from(selectedCheckboxes).map(cb => cb.value);
            const amount = document.getElementById('bulkAmount').value;
            const description = document.getElementById('bulkDescription').value;

            fetch('/admin/client-balance/bulk-add-balance', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({ client_ids: clientIds, amount, description })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                alert('An error occurred: ' + error.message);
            });
        });

        // Close modals when clicking outside
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.classList.remove('show');
                }
            });
        });
    </script>
</x-admin-layout>
