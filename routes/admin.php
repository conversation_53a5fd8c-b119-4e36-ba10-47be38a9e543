<?php

use App\Http\Controllers\AdminAuthController;
use App\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Route;

// Guest admin routes (not authenticated)
Route::middleware('guest')->group(function () {
    Route::get('/login', [AdminAuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AdminAuthController::class, 'login'])->name('login.post');
});

// Authenticated admin routes
Route::middleware('auth')->group(function () {
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
    
    // Client management
    Route::get('/clients', [AdminController::class, 'clients'])->name('clients.index');
    Route::get('/clients/{client}', [AdminController::class, 'showClient'])->name('clients.show');
    Route::patch('/clients/{client}/status', [AdminController::class, 'updateClientStatus'])->name('clients.status');
    
    // SMS management
    Route::get('/sms', [AdminController::class, 'sms'])->name('sms.index');
    
    // Profile management
    Route::get('/profile', [AdminController::class, 'profile'])->name('profile');
    Route::patch('/profile', [AdminController::class, 'updateProfile'])->name('profile.update');
    
    // Logout
    Route::post('/logout', [AdminAuthController::class, 'logout'])->name('logout');
});
