<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\DB;

class Client extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $guarded = [];

    protected $hidden = [
        'password',
        'authorization',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'balance' => 'decimal:2',
    ];

    /**
     * Get the SMS messages for the client.
     */
    public function sms(): HasMany
    {
        return $this->hasMany(SMS::class);
    }

    /**
     * Get the SMS responses for the client.
     */
    public function smsResponses(): HasMany
    {
        return $this->hasMany(SMSResponse::class);
    }

    /**
     * Get the balance transactions for the client.
     */
    public function balanceTransactions(): HasMany
    {
        return $this->hasMany(BalanceTransaction::class)->orderBy('created_at', 'desc');
    }

    /**
     * Add balance to the client account.
     */
    public function addBalance(float $amount, string $description = null, string $reference = null, string $source = 'admin', int $createdBy = null): BalanceTransaction
    {
        return DB::transaction(function () use ($amount, $description, $reference, $source, $createdBy) {
            $this->lockForUpdate();
            
            $balanceBefore = $this->balance;
            $balanceAfter = $balanceBefore + $amount;
            
            $this->update(['balance' => $balanceAfter]);
            
            return $this->balanceTransactions()->create([
                'type' => 'credit',
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'description' => $description ?: "Balance added",
                'reference' => $reference,
                'source' => $source,
                'created_by' => $createdBy,
            ]);
        });
    }

    /**
     * Deduct balance from the client account.
     */
    public function deductBalance(float $amount, string $description = null, string $reference = null, string $source = 'sms', int $createdBy = null): BalanceTransaction
    {
        return DB::transaction(function () use ($amount, $description, $reference, $source, $createdBy) {
            $this->lockForUpdate();
            
            if ($this->balance < $amount) {
                throw new \Exception('Insufficient balance. Current balance: ৳' . number_format($this->balance, 2));
            }
            
            $balanceBefore = $this->balance;
            $balanceAfter = $balanceBefore - $amount;
            
            $this->update(['balance' => $balanceAfter]);
            
            return $this->balanceTransactions()->create([
                'type' => 'debit',
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'description' => $description ?: "Balance deducted",
                'reference' => $reference,
                'source' => $source,
                'created_by' => $createdBy,
            ]);
        });
    }

    /**
     * Check if client has sufficient balance.
     */
    public function hasSufficientBalance(float $amount): bool
    {
        return $this->balance >= $amount;
    }

    /**
     * Get formatted balance.
     */
    public function getFormattedBalanceAttribute(): string
    {
        return '৳' . number_format($this->balance, 2);
    }

    /**
     * Get total credited amount.
     */
    public function getTotalCreditedAttribute(): float
    {
        return $this->balanceTransactions()->credit()->sum('amount');
    }

    /**
     * Get total debited amount.
     */
    public function getTotalDebitedAttribute(): float
    {
        return $this->balanceTransactions()->debit()->sum('amount');
    }
}
