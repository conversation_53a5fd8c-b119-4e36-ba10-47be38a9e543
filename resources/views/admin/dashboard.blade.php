@extends('admin.layouts.app')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')

@section('content')
<div class="space-y-6">
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Clients -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div class="flex items-center">
                <div class="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Clients</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($totalClients) }}</p>
                </div>
            </div>
        </div>

        <!-- Active Clients -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div class="flex items-center">
                <div class="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active Clients</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($activeClients) }}</p>
                </div>
            </div>
        </div>

        <!-- Total SMS -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div class="flex items-center">
                <div class="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total SMS</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($totalSMS) }}</p>
                </div>
            </div>
        </div>

        <!-- Today's SMS -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div class="flex items-center">
                <div class="flex items-center justify-center w-12 h-12 bg-orange-100 rounded-lg">
                    <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Today's SMS</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($todaySMS) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- SMS Status Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- SMS Status Chart -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">SMS Status Distribution</h3>
            <div class="space-y-4">
                <!-- Sent -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-green-500 rounded-full mr-3"></div>
                        <span class="text-sm font-medium text-gray-700">Sent</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-sm font-semibold text-gray-900 mr-2">{{ number_format($smsStats['sent']) }}</span>
                        <div class="w-24 bg-gray-200 rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: {{ $totalSMS > 0 ? ($smsStats['sent'] / $totalSMS) * 100 : 0 }}%"></div>
                        </div>
                    </div>
                </div>

                <!-- Pending -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-yellow-500 rounded-full mr-3"></div>
                        <span class="text-sm font-medium text-gray-700">Pending</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-sm font-semibold text-gray-900 mr-2">{{ number_format($smsStats['pending']) }}</span>
                        <div class="w-24 bg-gray-200 rounded-full h-2">
                            <div class="bg-yellow-500 h-2 rounded-full" style="width: {{ $totalSMS > 0 ? ($smsStats['pending'] / $totalSMS) * 100 : 0 }}%"></div>
                        </div>
                    </div>
                </div>

                <!-- Failed -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-red-500 rounded-full mr-3"></div>
                        <span class="text-sm font-medium text-gray-700">Failed</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-sm font-semibold text-gray-900 mr-2">{{ number_format($smsStats['failed']) }}</span>
                        <div class="w-24 bg-gray-200 rounded-full h-2">
                            <div class="bg-red-500 h-2 rounded-full" style="width: {{ $totalSMS > 0 ? ($smsStats['failed'] / $totalSMS) * 100 : 0 }}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div class="space-y-3">
                <a href="{{ route('admin.clients.index') }}" 
                   class="flex items-center p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors duration-200">
                    <svg class="w-5 h-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                    <span class="text-sm font-medium text-blue-700">Manage Clients</span>
                </a>

                <a href="{{ route('admin.sms.index') }}" 
                   class="flex items-center p-3 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors duration-200">
                    <svg class="w-5 h-5 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    <span class="text-sm font-medium text-purple-700">View SMS Messages</span>
                </a>

                <a href="{{ route('admin.profile') }}" 
                   class="flex items-center p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-colors duration-200">
                    <svg class="w-5 h-5 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <span class="text-sm font-medium text-green-700">Update Profile</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent SMS Messages -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Recent SMS Messages</h3>
                    <a href="{{ route('admin.sms.index') }}" class="text-sm text-blue-600 hover:text-blue-700">View All</a>
                </div>
            </div>
            <div class="p-6">
                @if($recentSMS->count() > 0)
                    <div class="space-y-4">
                        @foreach($recentSMS as $sms)
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    @if($sms->status === 'sent')
                                        <div class="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                                    @elseif($sms->status === 'pending')
                                        <div class="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                                    @else
                                        <div class="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                                    @endif
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm text-gray-900 truncate">{{ Str::limit($sms->msg, 50) }}</p>
                                    <p class="text-xs text-gray-500">
                                        {{ $sms->client->name ?? 'Unknown' }} • {{ $sms->created_at->diffForHumans() }}
                                    </p>
                                </div>
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        @if($sms->status === 'sent') bg-green-100 text-green-800
                                        @elseif($sms->status === 'pending') bg-yellow-100 text-yellow-800
                                        @else bg-red-100 text-red-800 @endif">
                                        {{ ucfirst($sms->status) }}
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 text-center py-4">No SMS messages found.</p>
                @endif
            </div>
        </div>

        <!-- Recent Clients -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Recent Clients</h3>
                    <a href="{{ route('admin.clients.index') }}" class="text-sm text-blue-600 hover:text-blue-700">View All</a>
                </div>
            </div>
            <div class="p-6">
                @if($recentClients->count() > 0)
                    <div class="space-y-4">
                        @foreach($recentClients as $client)
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center">
                                        <span class="text-xs font-medium text-white">{{ substr($client->name, 0, 1) }}</span>
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">{{ $client->name }}</p>
                                    <p class="text-xs text-gray-500">{{ $client->email }}</p>
                                </div>
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        @if($client->status === '2') bg-green-100 text-green-800
                                        @elseif($client->status === '1') bg-yellow-100 text-yellow-800
                                        @else bg-red-100 text-red-800 @endif">
                                        @if($client->status === '2') Active
                                        @elseif($client->status === '1') Pending
                                        @else Inactive @endif
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 text-center py-4">No clients found.</p>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
