@extends('admin.layouts.app')

@section('title', 'Admin Panel')
@section('page-title', '')

@section('content')
<!-- Enhanced Welcome Header with Dark Mode Support -->
<div class="relative bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 dark:from-slate-800 dark:via-slate-700 dark:to-slate-600 rounded-3xl p-8 mb-8 text-white shadow-2xl border border-purple-200 dark:border-slate-600 overflow-hidden transition-all duration-500">
    <!-- Animated Background Elements -->
    <div class="absolute top-0 right-0 w-40 h-40 bg-white bg-opacity-10 dark:bg-white dark:bg-opacity-5 rounded-full -mr-20 -mt-20 animate-pulse"></div>
    <div class="absolute bottom-0 left-0 w-32 h-32 bg-white bg-opacity-10 dark:bg-white dark:bg-opacity-5 rounded-full -ml-16 -mb-16 animate-bounce"></div>
    <div class="absolute top-1/2 right-1/4 w-4 h-4 bg-white bg-opacity-30 dark:bg-white dark:bg-opacity-20 rounded-full animate-ping"></div>
    <div class="absolute top-1/4 left-3/4 w-2 h-2 bg-white bg-opacity-50 dark:bg-white dark:bg-opacity-30 rounded-full animate-pulse"></div>
    
    <div class="relative z-10">
        <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between">
            <div class="mb-6 lg:mb-0 flex-1">
                <div class="flex items-center mb-4">
                    <div class="bg-white bg-opacity-20 dark:bg-white dark:bg-opacity-15 p-3 rounded-2xl mr-4 backdrop-blur-sm border border-white/10">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-4xl lg:text-5xl font-bold mb-2 text-white drop-shadow-lg">
                            Welcome back, {{ Auth::user()->name }}! 
                            <span class="text-3xl animate-wave inline-block">👋</span>
                        </h1>
                        <p class="text-purple-100 dark:text-slate-300 text-lg lg:text-xl mb-3 drop-shadow-sm">
                            Your SMS City admin dashboard is ready to manage communications
                        </p>
                    </div>
                </div>
                
                <div class="flex flex-wrap items-center gap-4 text-purple-200 dark:text-slate-300">
                    <div class="flex items-center bg-white bg-opacity-15 dark:bg-white dark:bg-opacity-10 px-4 py-2 rounded-full backdrop-blur-sm border border-white/10">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-sm font-medium">{{ now()->format('l, F j, Y • g:i A') }}</span>
                    </div>
                    <div class="flex items-center bg-white bg-opacity-15 dark:bg-white dark:bg-opacity-10 px-4 py-2 rounded-full backdrop-blur-sm border border-white/10">
                        <div class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                        <span class="text-sm font-medium">System Online</span>
                    </div>
                </div>
            </div>
            
            <div class="bg-white bg-opacity-15 dark:bg-white dark:bg-opacity-10 rounded-3xl p-6 backdrop-blur-md border border-white/20 min-w-0 shadow-xl">
                <div class="text-center">
                    <div class="text-3xl font-bold text-white mb-2 drop-shadow-lg">৳{{ number_format($totalSMS) }}</div>
                    <div class="text-purple-100 dark:text-slate-300 text-sm mb-3">Total Revenue</div>
                    <div class="flex items-center justify-center space-x-4 text-xs">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-green-400 rounded-full mr-1 shadow-sm"></div>
                            <span>৳{{ number_format($smsStats['sent'] ?? 0) }} Earned</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-yellow-400 rounded-full mr-1 shadow-sm"></div>
                            <span>৳{{ number_format($smsStats['pending'] ?? 0) }} Pending</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="space-y-8">
    <!-- Enhanced Statistics Grid with Dark Mode -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Clients Card -->
        <div class="group bg-white dark:bg-slate-800 rounded-3xl shadow-lg dark:shadow-slate-700/50 p-6 border border-gray-100 dark:border-slate-700 hover:shadow-2xl hover:border-blue-200 dark:hover:border-blue-600 hover:shadow-blue-500/20 transition-all duration-500 transform hover:-translate-y-2 relative overflow-hidden">
            <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full -mr-10 -mt-10 opacity-10 group-hover:opacity-20 transition-opacity duration-300"></div>
            <div class="relative z-10">
                <div class="flex items-center justify-between mb-4">
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/50 dark:to-blue-800/50 p-4 rounded-2xl group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <div class="text-right">
                        <div class="text-3xl font-bold text-gray-800 dark:text-slate-100 mb-1">{{ number_format($totalClients) }}</div>
                        <div class="text-sm text-gray-500 dark:text-slate-400 font-medium">Total Clients</div>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center text-blue-600 dark:text-blue-400">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        <span class="text-sm font-medium">All registered</span>
                    </div>
                    <div class="text-xs text-gray-400 dark:text-slate-500">{{ $activeClients }}/{{ $totalClients }} active</div>
                </div>
            </div>
        </div>

        <!-- Active Clients Card -->
        <div class="group bg-white dark:bg-slate-800 rounded-3xl shadow-lg dark:shadow-slate-700/50 p-6 border border-gray-100 dark:border-slate-700 hover:shadow-2xl hover:border-green-200 dark:hover:border-green-600 hover:shadow-green-500/20 transition-all duration-500 transform hover:-translate-y-2 relative overflow-hidden">
            <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full -mr-10 -mt-10 opacity-10 group-hover:opacity-20 transition-opacity duration-300"></div>
            <div class="relative z-10">
                <div class="flex items-center justify-between mb-4">
                    <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/50 dark:to-green-800/50 p-4 rounded-2xl group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <svg class="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="text-right">
                        <div class="text-3xl font-bold text-gray-800 dark:text-slate-100 mb-1">৳{{ number_format($activeClients * 100) }}</div>
                        <div class="text-sm text-gray-500 dark:text-slate-400 font-medium">Active Balance</div>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center text-green-600 dark:text-green-400">
                        <div class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse shadow-lg shadow-green-500/50"></div>
                        <span class="text-sm font-medium">Currently active</span>
                    </div>
                    <div class="text-xs text-gray-400 dark:text-slate-500">{{ round(($activeClients / max($totalClients, 1)) * 100) }}% rate</div>
                </div>
            </div>
        </div>

        <!-- Total SMS Card -->
        <div class="group bg-white dark:bg-slate-800 rounded-3xl shadow-lg dark:shadow-slate-700/50 p-6 border border-gray-100 dark:border-slate-700 hover:shadow-2xl hover:border-purple-200 dark:hover:border-purple-600 hover:shadow-purple-500/20 transition-all duration-500 transform hover:-translate-y-2 relative overflow-hidden">
            <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full -mr-10 -mt-10 opacity-10 group-hover:opacity-20 transition-opacity duration-300"></div>
            <div class="relative z-10">
                <div class="flex items-center justify-between mb-4">
                    <div class="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/50 dark:to-purple-800/50 p-4 rounded-2xl group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <svg class="w-8 h-8 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    </div>
                    <div class="text-right">
                        <div class="text-3xl font-bold text-gray-800 dark:text-slate-100 mb-1">৳{{ number_format($totalSMS * 0.5) }}</div>
                        <div class="text-sm text-gray-500 dark:text-slate-400 font-medium">Total Revenue</div>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center text-purple-600 dark:text-purple-400">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                        </svg>
                        <span class="text-sm font-medium">All time</span>
                    </div>
                    <div class="text-xs text-gray-400 dark:text-slate-500">৳{{ number_format($todaySMS * 0.5) }} today</div>
                </div>
            </div>
        </div>

        <!-- Today's SMS Card -->
        <div class="group bg-white dark:bg-slate-800 rounded-3xl shadow-lg dark:shadow-slate-700/50 p-6 border border-gray-100 dark:border-slate-700 hover:shadow-2xl hover:border-orange-200 dark:hover:border-orange-600 hover:shadow-orange-500/20 transition-all duration-500 transform hover:-translate-y-2 relative overflow-hidden">
            <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full -mr-10 -mt-10 opacity-10 group-hover:opacity-20 transition-opacity duration-300"></div>
            <div class="relative z-10">
                <div class="flex items-center justify-between mb-4">
                    <div class="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/50 dark:to-orange-800/50 p-4 rounded-2xl group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <svg class="w-8 h-8 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="text-right">
                        <div class="text-3xl font-bold text-gray-800 dark:text-slate-100 mb-1">৳{{ number_format($todaySMS * 0.5) }}</div>
                        <div class="text-sm text-gray-500 dark:text-slate-400 font-medium">Today's Revenue</div>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center text-orange-600 dark:text-orange-400">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        <span class="text-sm font-medium">৳{{ number_format($todaySMS * 2 * 0.1) }} API cost</span>
                    </div>
                    <div class="text-xs text-gray-400 dark:text-slate-500">Live data</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Section with Dark Mode -->
    <div class="bg-white dark:bg-slate-800 rounded-2xl shadow-lg dark:shadow-slate-700/50 p-8 border border-gray-100 dark:border-slate-700 mb-8 transition-all duration-300">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-bold text-black dark:text-slate-100">Quick Actions</h3>
            <div class="bg-indigo-50 dark:bg-indigo-900/50 p-2 rounded-lg">
                <svg class="w-5 h-5 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
            </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <a href="{{ route('admin.clients.index') }}" class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/30 p-6 rounded-xl border border-blue-200 dark:border-blue-700 hover:from-blue-100 hover:to-blue-200 dark:hover:from-blue-800/50 dark:hover:to-blue-700/50 hover:shadow-xl hover:shadow-blue-500/20 transition-all duration-300 group">
                <div class="flex items-center space-x-4">
                    <div class="bg-blue-600 dark:bg-blue-500 p-3 rounded-lg group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-semibold text-blue-900 dark:text-blue-100">Manage Clients</h4>
                        <p class="text-sm text-blue-600 dark:text-blue-300">View and manage clients</p>
                    </div>
                </div>
            </a>
            <a href="{{ route('admin.sms.index') }}" class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/30 dark:to-green-800/30 p-6 rounded-xl border border-green-200 dark:border-green-700 hover:from-green-100 hover:to-green-200 dark:hover:from-green-800/50 dark:hover:to-green-700/50 hover:shadow-xl hover:shadow-green-500/20 transition-all duration-300 group">
                <div class="flex items-center space-x-4">
                    <div class="bg-green-600 dark:bg-green-500 p-3 rounded-lg group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-semibold text-green-900 dark:text-green-100">SMS Messages</h4>
                        <p class="text-sm text-green-600 dark:text-green-300">View all messages</p>
                    </div>
                </div>
            </a>
            <a href="{{ route('admin.profile') }}" class="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/30 dark:to-purple-800/30 p-6 rounded-xl border border-purple-200 dark:border-purple-700 hover:from-purple-100 hover:to-purple-200 dark:hover:from-purple-800/50 dark:hover:to-purple-700/50 hover:shadow-xl hover:shadow-purple-500/20 transition-all duration-300 group">
                <div class="flex items-center space-x-4">
                    <div class="bg-purple-600 dark:bg-purple-500 p-3 rounded-lg group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-semibold text-purple-900 dark:text-purple-100">Admin Profile</h4>
                        <p class="text-sm text-purple-600 dark:text-purple-300">Manage your profile</p>
                    </div>
                </div>
            </a>
        </div>
    </div>

    <!-- SMS Status Overview with Dark Mode -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- SMS Status Chart -->
        <div class="bg-white dark:bg-slate-800 rounded-2xl shadow-lg dark:shadow-slate-700/50 p-8 border border-gray-100 dark:border-slate-700 hover:shadow-xl dark:hover:shadow-slate-600/50 transition-all duration-300">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-black dark:text-slate-100">Revenue Distribution</h3>
                <div class="bg-blue-50 dark:bg-blue-900/50 p-2 rounded-lg">
                    <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
            </div>
            <div class="space-y-6">
                <!-- Sent -->
                <div class="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-xl border border-green-100 dark:border-green-800 hover:shadow-md dark:hover:shadow-green-900/30 transition-all duration-200">
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-green-500 rounded-full mr-4 shadow-lg shadow-green-500/50"></div>
                        <span class="text-sm font-semibold text-black dark:text-slate-100">Earned</span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-lg font-bold text-black dark:text-slate-100">৳{{ number_format($smsStats['sent'] * 0.5) }}</span>
                        <div class="w-32 bg-green-200 dark:bg-green-800 rounded-full h-3 shadow-inner">
                            <div class="bg-gradient-to-r from-green-400 to-green-500 h-3 rounded-full shadow-sm transition-all duration-1000" 
                                 style="width: {{ $totalSMS > 0 ? ($smsStats['sent'] / $totalSMS) * 100 : 0 }}%"></div>
                        </div>
                        <span class="text-sm font-medium text-green-700 dark:text-green-400">{{ $totalSMS > 0 ? number_format(($smsStats['sent'] / $totalSMS) * 100, 1) : 0 }}%</span>
                    </div>
                </div>

                <!-- Pending -->
                <div class="flex items-center justify-between p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-xl border border-yellow-100 dark:border-yellow-800 hover:shadow-md dark:hover:shadow-yellow-900/30 transition-all duration-200">
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-yellow-500 rounded-full mr-4 shadow-lg shadow-yellow-500/50"></div>
                        <span class="text-sm font-semibold text-black dark:text-slate-100">Pending</span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-lg font-bold text-black dark:text-slate-100">৳{{ number_format($smsStats['pending'] * 0.5) }}</span>
                        <div class="w-32 bg-yellow-200 dark:bg-yellow-800 rounded-full h-3 shadow-inner">
                            <div class="bg-gradient-to-r from-yellow-400 to-yellow-500 h-3 rounded-full shadow-sm transition-all duration-1000" 
                                 style="width: {{ $totalSMS > 0 ? ($smsStats['pending'] / $totalSMS) * 100 : 0 }}%"></div>
                        </div>
                        <span class="text-sm font-medium text-yellow-700 dark:text-yellow-400">{{ $totalSMS > 0 ? number_format(($smsStats['pending'] / $totalSMS) * 100, 1) : 0 }}%</span>
                    </div>
                </div>

                <!-- Failed -->
                <div class="flex items-center justify-between p-4 bg-red-50 dark:bg-red-900/20 rounded-xl border border-red-100 dark:border-red-800 hover:shadow-md dark:hover:shadow-red-900/30 transition-all duration-200">
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-red-500 rounded-full mr-4 shadow-lg shadow-red-500/50"></div>
                        <span class="text-sm font-semibold text-black dark:text-slate-100">Lost</span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-lg font-bold text-black dark:text-slate-100">৳{{ number_format($smsStats['failed'] * 0.5) }}</span>
                        <div class="w-32 bg-red-200 dark:bg-red-800 rounded-full h-3 shadow-inner">
                            <div class="bg-gradient-to-r from-red-400 to-red-500 h-3 rounded-full shadow-sm transition-all duration-1000" 
                                 style="width: {{ $totalSMS > 0 ? ($smsStats['failed'] / $totalSMS) * 100 : 0 }}%"></div>
                        </div>
                        <span class="text-sm font-medium text-red-700 dark:text-red-400">{{ $totalSMS > 0 ? number_format(($smsStats['failed'] / $totalSMS) * 100, 1) : 0 }}%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Health Dashboard -->
        <div class="bg-white dark:bg-slate-800 rounded-2xl shadow-lg dark:shadow-slate-700/50 p-8 border border-gray-100 dark:border-slate-700 hover:shadow-xl dark:hover:shadow-slate-600/50 transition-all duration-300">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-black dark:text-slate-100">Financial Overview</h3>
                <div class="bg-emerald-50 dark:bg-emerald-900/50 p-2 rounded-lg">
                    <svg class="w-5 h-5 text-emerald-600 dark:text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
            </div>
            <div class="space-y-4">
                <div class="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-xl border border-green-100 dark:border-green-800">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-3 animate-pulse shadow-lg shadow-green-500/50"></div>
                        <span class="text-sm font-semibold text-black dark:text-slate-100">Monthly Revenue</span>
                    </div>
                    <span class="text-sm font-bold text-green-700 dark:text-green-400">৳{{ number_format($totalSMS * 0.5 * 30) }}</span>
                </div>
                
                <div class="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-100 dark:border-blue-800">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-blue-500 rounded-full mr-3 shadow-lg shadow-blue-500/50"></div>
                        <span class="text-sm font-semibold text-black dark:text-slate-100">Daily Revenue</span>
                    </div>
                    <span class="text-sm font-bold text-blue-700 dark:text-blue-400">৳{{ number_format($todaySMS * 0.5) }}</span>
                </div>
                
                <div class="flex items-center justify-between p-4 bg-purple-50 dark:bg-purple-900/20 rounded-xl border border-purple-100 dark:border-purple-800">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-purple-500 rounded-full mr-3 shadow-lg shadow-purple-500/50"></div>
                        <span class="text-sm font-semibold text-black dark:text-slate-100">Total Balance</span>
                    </div>
                    <span class="text-sm font-bold text-purple-700 dark:text-purple-400">৳{{ number_format(($totalClients * 100) + ($totalSMS * 0.5)) }}</span>
                </div>
                
                <div class="flex items-center justify-between p-4 bg-orange-50 dark:bg-orange-900/20 rounded-xl border border-orange-100 dark:border-orange-800">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-orange-500 rounded-full mr-3 shadow-lg shadow-orange-500/50"></div>
                        <span class="text-sm font-semibold text-black dark:text-slate-100">Profit Margin</span>
                    </div>
                    <span class="text-sm font-bold text-orange-700 dark:text-orange-400">85%</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity with Dark Mode -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent SMS Messages -->
        <div class="bg-white dark:bg-slate-800 rounded-2xl shadow-lg dark:shadow-slate-700/50 border border-gray-100 dark:border-slate-700 hover:shadow-xl dark:hover:shadow-slate-600/50 transition-all duration-300">
            <div class="p-6 border-b border-gray-200 dark:border-slate-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-t-2xl">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="bg-blue-500 dark:bg-blue-600 p-2 rounded-lg mr-3 shadow-lg">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-black dark:text-slate-100">Recent SMS Messages</h3>
                    </div>
                    <a href="{{ route('admin.sms.index') }}" class="text-sm font-semibold text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 bg-white dark:bg-slate-800 px-3 py-2 rounded-lg hover:bg-blue-50 dark:hover:bg-slate-700 border border-blue-200 dark:border-slate-600 transition-all duration-200 shadow-sm">View All</a>
                </div>
            </div>
            <div class="p-6">
                @if($recentSMS->count() > 0)
                    <div class="space-y-4">
                        @foreach($recentSMS as $sms)
                            <div class="flex items-start space-x-4 p-4 bg-gray-50 dark:bg-slate-700/50 rounded-xl hover:bg-gray-100 dark:hover:bg-slate-700 transition-all duration-200 border border-gray-100 dark:border-slate-600">
                                <div class="flex-shrink-0">
                                    @if($sms->status === 'sent')
                                        <div class="w-3 h-3 bg-green-500 rounded-full mt-2 shadow-lg shadow-green-500/50"></div>
                                    @elseif($sms->status === 'pending')
                                        <div class="w-3 h-3 bg-yellow-500 rounded-full mt-2 shadow-lg shadow-yellow-500/50"></div>
                                    @else
                                        <div class="w-3 h-3 bg-red-500 rounded-full mt-2 shadow-lg shadow-red-500/50"></div>
                                    @endif
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-semibold text-black dark:text-slate-100 truncate mb-1">{{ Str::limit($sms->msg, 50) }}</p>
                                    <p class="text-xs text-gray-600 dark:text-slate-400 flex items-center">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                        {{ $sms->client->name ?? 'Unknown' }} • {{ $sms->created_at->diffForHumans() }}
                                    </p>
                                </div>
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold shadow-sm
                                        @if($sms->status === 'sent') bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 border border-green-200 dark:border-green-800
                                        @elseif($sms->status === 'pending') bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400 border border-yellow-200 dark:border-yellow-800
                                        @else bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400 border border-red-200 dark:border-red-800 @endif">
                                        {{ ucfirst($sms->status) }}
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <div class="bg-gray-100 dark:bg-slate-700 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-gray-400 dark:text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                            </svg>
                        </div>
                        <p class="text-gray-500 dark:text-slate-400 font-medium">No SMS messages found.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Recent Clients -->
        <div class="bg-white dark:bg-slate-800 rounded-2xl shadow-lg dark:shadow-slate-700/50 border border-gray-100 dark:border-slate-700 hover:shadow-xl dark:hover:shadow-slate-600/50 transition-all duration-300">
            <div class="p-6 border-b border-gray-200 dark:border-slate-700 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/30 dark:to-emerald-900/30 rounded-t-2xl">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="bg-green-500 dark:bg-green-600 p-2 rounded-lg mr-3 shadow-lg">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-black dark:text-slate-100">Recent Clients</h3>
                    </div>
                    <a href="{{ route('admin.clients.index') }}" class="text-sm font-semibold text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 bg-white dark:bg-slate-800 px-3 py-2 rounded-lg hover:bg-green-50 dark:hover:bg-slate-700 border border-green-200 dark:border-slate-600 transition-all duration-200 shadow-sm">View All</a>
                </div>
            </div>
            <div class="p-6">
                @if($recentClients->count() > 0)
                    <div class="space-y-4">
                        @foreach($recentClients as $client)
                            <div class="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-slate-700/50 rounded-xl hover:bg-gray-100 dark:hover:bg-slate-700 transition-all duration-200 border border-gray-100 dark:border-slate-600">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-500 dark:to-indigo-500 rounded-xl flex items-center justify-center shadow-lg">
                                        <span class="text-sm font-bold text-white">{{ substr($client->name, 0, 1) }}</span>
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-bold text-black dark:text-slate-100 truncate">{{ $client->name }}</p>
                                    <p class="text-xs text-gray-600 dark:text-slate-400 flex items-center mt-1">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                        {{ $client->email }}
                                    </p>
                                </div>
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold shadow-sm
                                        @if($client->status === '2') bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 border border-green-200 dark:border-green-800
                                        @elseif($client->status === '1') bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400 border border-yellow-200 dark:border-yellow-800
                                        @else bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400 border border-red-200 dark:border-red-800 @endif">
                                        @if($client->status === '2') Active
                                        @elseif($client->status === '1') Pending
                                        @else Inactive @endif
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <div class="bg-gray-100 dark:bg-slate-700 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-gray-400 dark:text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <p class="text-gray-500 dark:text-slate-400 font-medium">No clients found.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Custom CSS for Enhanced Dashboard Animations -->
<style>
/* Wave animation for emoji */
@keyframes wave {
    0% { transform: rotate(0deg); }
    10% { transform: rotate(14deg); }
    20% { transform: rotate(-8deg); }
    30% { transform: rotate(14deg); }
    40% { transform: rotate(-4deg); }
    50% { transform: rotate(10deg); }
    60% { transform: rotate(0deg); }
    100% { transform: rotate(0deg); }
}

.animate-wave {
    animation: wave 2s ease-in-out infinite;
    transform-origin: 70% 70%;
}

/* Floating animation for cards */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

/* Gradient animation for header */
@keyframes gradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.animate-gradient {
    background-size: 200% 200%;
    animation: gradient 15s ease infinite;
}

/* Pulse glow effect */
@keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.4); }
    50% { box-shadow: 0 0 40px rgba(59, 130, 246, 0.8); }
}

.pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
}

/* Dark mode transitions */
* {
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Enhanced dark mode shadows */
.dark .shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

.dark .shadow-xl {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

.dark .shadow-2xl {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
}

/* Improved hover effects for dark mode */
.dark .group:hover .shadow-lg {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* Better contrast for text in dark mode */
.dark .text-gray-800 {
    color: rgb(248 250 252);
}

.dark .text-gray-500 {
    color: rgb(148 163 184);
}

.dark .text-gray-400 {
    color: rgb(100 116 139);
}

/* Enhanced backdrop blur for dark mode */
.dark .backdrop-blur-sm {
    backdrop-filter: blur(4px) brightness(0.8);
}

.dark .backdrop-blur-md {
    backdrop-filter: blur(12px) brightness(0.8);
}
</style>
@endsection
