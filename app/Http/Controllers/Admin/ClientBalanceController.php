<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\BalanceTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ClientBalanceController extends Controller
{
    /**
     * Display client balance management page.
     */
    public function index(Request $request)
    {
        $query = Client::with(['balanceTransactions' => function($q) {
            $q->latest()->limit(5);
        }]);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('authorization', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by balance range
        if ($request->filled('min_balance')) {
            $query->where('balance', '>=', $request->min_balance);
        }
        if ($request->filled('max_balance')) {
            $query->where('balance', '<=', $request->max_balance);
        }

        $clients = $query->paginate(20)->withQueryString();

        // Summary statistics
        $totalClients = Client::count();
        $totalBalance = Client::sum('balance');
        $lowBalanceClients = Client::where('balance', '<', 10)->count();
        $recentTransactions = BalanceTransaction::with(['client', 'createdBy'])
            ->latest()
            ->limit(10)
            ->get();

        return view('admin.client-balance.index', compact(
            'clients',
            'totalClients',
            'totalBalance',
            'lowBalanceClients',
            'recentTransactions'
        ));
    }

    /**
     * Show balance details for a specific client.
     */
    public function show(Client $client)
    {
        $transactions = $client->balanceTransactions()
            ->with(['createdBy', 'sms'])
            ->paginate(20);

        $stats = [
            'total_credited' => $client->balanceTransactions()->credit()->sum('amount'),
            'total_debited' => $client->balanceTransactions()->debit()->sum('amount'),
            'total_transactions' => $client->balanceTransactions()->count(),
            'sms_spent' => $client->balanceTransactions()->bySource('sms')->sum('amount'),
        ];

        return view('admin.client-balance.show', compact('client', 'transactions', 'stats'));
    }

    /**
     * Add balance to client account.
     */
    public function addBalance(Request $request, Client $client)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01|max:99999.99',
            'description' => 'nullable|string|max:255',
        ]);

        try {
            $transaction = $client->addBalance(
                amount: $request->amount,
                description: $request->description ?: "Balance added by admin",
                source: 'admin',
                createdBy: Auth::id()
            );

            return response()->json([
                'success' => true,
                'message' => "Successfully added ৳{$request->amount} to {$client->name}'s account",
                'new_balance' => $client->fresh()->formatted_balance,
                'transaction' => $transaction
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add balance: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Deduct balance from client account.
     */
    public function deductBalance(Request $request, Client $client)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01|max:' . $client->balance,
            'description' => 'nullable|string|max:255',
        ]);

        try {
            $transaction = $client->deductBalance(
                amount: $request->amount,
                description: $request->description ?: "Balance deducted by admin",
                source: 'adjustment',
                createdBy: Auth::id()
            );

            return response()->json([
                'success' => true,
                'message' => "Successfully deducted ৳{$request->amount} from {$client->name}'s account",
                'new_balance' => $client->fresh()->formatted_balance,
                'transaction' => $transaction
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to deduct balance: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Bulk add balance to multiple clients.
     */
    public function bulkAddBalance(Request $request)
    {
        $request->validate([
            'client_ids' => 'required|array',
            'client_ids.*' => 'exists:clients,id',
            'amount' => 'required|numeric|min:0.01|max:99999.99',
            'description' => 'nullable|string|max:255',
        ]);

        try {
            DB::transaction(function () use ($request) {
                $clients = Client::whereIn('id', $request->client_ids)->get();
                
                foreach ($clients as $client) {
                    $client->addBalance(
                        amount: $request->amount,
                        description: $request->description ?: "Bulk balance addition by admin",
                        source: 'admin',
                        createdBy: Auth::id()
                    );
                }
            });

            $clientCount = count($request->client_ids);
            return response()->json([
                'success' => true,
                'message' => "Successfully added ৳{$request->amount} to {$clientCount} clients"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add balance: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Get balance transaction history.
     */
    public function transactionHistory(Request $request)
    {
        $query = BalanceTransaction::with(['client', 'createdBy']);

        // Filter by client
        if ($request->filled('client_id')) {
            $query->where('client_id', $request->client_id);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by source
        if ($request->filled('source')) {
            $query->where('source', $request->source);
        }

        // Filter by date range
        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }
        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        $transactions = $query->latest()->paginate(50);

        return view('admin.client-balance.transactions', compact('transactions'));
    }
}
