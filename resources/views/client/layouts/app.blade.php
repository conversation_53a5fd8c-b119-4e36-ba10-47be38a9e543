<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    @php
        $errors = session('errors', new \Illuminate\Support\MessageBag());
    @endphp

    <title>{{ config('app.name', 'Laravel') }} - @yield('title', 'Client Portal')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <style>
        /* CSS Variables for Dark Mode */
        :root {
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --sidebar-bg: #1f2937;
            --sidebar-text: #d1d5db;
            --sidebar-hover: #374151;
            --header-bg: #ffffff;
            --card-bg: #ffffff;
            --accent-primary: #3b82f6;
            --accent-secondary: #10b981;
        }

        [data-theme="dark"] {
            --bg-primary: #111827;
            --bg-secondary: #1f2937;
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --border-color: #374151;
            --sidebar-bg: #0f172a;
            --sidebar-text: #cbd5e1;
            --sidebar-hover: #1e293b;
            --header-bg: #1f2937;
            --card-bg: #1f2937;
            --accent-primary: #60a5fa;
            --accent-secondary: #34d399;
        }

        /* Base Styles */
        body {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background: var(--sidebar-bg);
            z-index: 1000;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
            overflow-y: auto;
            border-right: 1px solid var(--border-color);
        }

        .sidebar.open {
            transform: translateX(0);
        }

        .sidebar-header {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            background: var(--sidebar-bg);
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .sidebar-nav a {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: var(--sidebar-text);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .sidebar-nav a:hover,
        .sidebar-nav a.active {
            background: var(--sidebar-hover);
            border-left-color: var(--accent-primary);
            color: #ffffff;
        }

        .sidebar-nav a i {
            width: 20px;
            margin-right: 0.75rem;
            text-align: center;
        }

        /* Header Styles */
        .main-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 64px;
            background: var(--header-bg);
            border-bottom: 1px solid var(--border-color);
            z-index: 999;
            transition: all 0.3s ease;
        }

        .main-content {
            margin-top: 64px;
            transition: margin-left 0.3s ease;
            min-height: calc(100vh - 64px);
            padding: 1.5rem;
        }

        .content-with-sidebar {
            margin-left: 250px;
        }

        /* Dark Mode Toggle */
        .dark-mode-toggle {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 0.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .dark-mode-toggle:hover {
            background: var(--accent-primary);
            color: #ffffff;
        }

        /* Header Dropdown */
        .dropdown {
            position: relative;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            min-width: 200px;
            z-index: 1000;
            opacity: 0;
            transform: translateY(-10px);
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .dropdown-menu.show {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        .dropdown-menu a {
            display: block;
            padding: 0.75rem 1rem;
            color: var(--text-primary);
            text-decoration: none;
            transition: background-color 0.2s ease;
        }

        .dropdown-menu a:hover {
            background: var(--bg-secondary);
        }

        /* Card Styles */
        .card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .content-with-sidebar {
                margin-left: 0;
            }
        }

        /* Overlay for mobile */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .sidebar-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* Utility classes */
        .text-primary { color: var(--text-primary); }
        .text-secondary { color: var(--text-secondary); }
        .bg-primary { background-color: var(--bg-primary); }
        .bg-secondary { background-color: var(--bg-secondary); }
        .border-color { border-color: var(--border-color); }
    </style>
</head>

<body>
    <div class="min-h-screen">
        <!-- Sidebar -->
        <aside id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <div style="display: flex; align-items: center; color: #ffffff;">
                    <i class="fas fa-sms" style="font-size: 1.5rem; margin-right: 0.75rem; color: var(--accent-primary);"></i>
                    <div>
                        <h1 style="font-size: 1.25rem; font-weight: 700; margin: 0;">SMS City</h1>
                        <p style="font-size: 0.75rem; color: var(--sidebar-text); margin: 0;">Client Portal</p>
                    </div>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <a href="{{ route('client.dashboard') }}" class="{{ request()->routeIs('client.dashboard') ? 'active' : '' }}">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="{{ route('client.sms-history') }}" class="{{ request()->routeIs('client.sms-history') ? 'active' : '' }}">
                    <i class="fas fa-history"></i>
                    <span>SMS History</span>
                </a>
                <a href="#" class="{{ request()->routeIs('client.send-sms') ? 'active' : '' }}">
                    <i class="fas fa-paper-plane"></i>
                    <span>Send SMS</span>
                </a>
                <a href="#" class="{{ request()->routeIs('client.contacts') ? 'active' : '' }}">
                    <i class="fas fa-address-book"></i>
                    <span>Contacts</span>
                </a>
                <a href="#" class="{{ request()->routeIs('client.billing') ? 'active' : '' }}">
                    <i class="fas fa-credit-card"></i>
                    <span>Billing</span>
                </a>
                <a href="#" class="{{ request()->routeIs('client.profile') ? 'active' : '' }}">
                    <i class="fas fa-user-cog"></i>
                    <span>Profile Settings</span>
                </a>
            </nav>
        </aside>

        <!-- Sidebar Overlay for Mobile -->
        <div id="sidebarOverlay" class="sidebar-overlay" onclick="toggleSidebar()"></div>

        <!-- Header -->
        <header class="main-header">
            <div style="display: flex; align-items: center; justify-content: space-between; height: 100%; padding: 0 1rem;">
                <!-- Left side -->
                <div style="display: flex; align-items: center;">
                    <button onclick="toggleSidebar()" style="background: none; border: none; color: var(--text-primary); font-size: 1.25rem; padding: 0.5rem; margin-right: 1rem; cursor: pointer; border-radius: 0.375rem; transition: background-color 0.2s ease;">
                        <i class="fas fa-bars"></i>
                    </button>
                    
                    @isset($header)
                        <h1 style="font-size: 1.5rem; font-weight: 600; color: var(--text-primary); margin: 0;">{{ $header }}</h1>
                    @endisset
                </div>

                <!-- Right side -->
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <!-- Dark Mode Toggle -->
                    <button id="darkModeToggle" class="dark-mode-toggle" onclick="toggleDarkMode()" title="Toggle Dark Mode">
                        <i class="fas fa-moon"></i>
                    </button>

                    <!-- User Dropdown -->
                    <div class="dropdown">
                        <button onclick="toggleDropdown()" style="display: flex; align-items: center; background: none; border: none; color: var(--text-primary); cursor: pointer; padding: 0.5rem; border-radius: 0.375rem; transition: background-color 0.2s ease;">
                            <div style="width: 32px; height: 32px; background: var(--accent-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #ffffff; font-weight: 600; margin-right: 0.5rem;">
                                {{ strtoupper(substr(Auth::guard('client')->user()->name, 0, 1)) }}
                            </div>
                            <span style="margin-right: 0.5rem;">{{ Auth::guard('client')->user()->name }}</span>
                            <i class="fas fa-chevron-down" style="font-size: 0.75rem;"></i>
                        </button>
                        
                        <div id="userDropdown" class="dropdown-menu">
                            <a href="#" style="display: flex; align-items: center;">
                                <i class="fas fa-user-circle" style="margin-right: 0.5rem;"></i>
                                Profile
                            </a>
                            <a href="#" style="display: flex; align-items: center;">
                                <i class="fas fa-cog" style="margin-right: 0.5rem;"></i>
                                Settings
                            </a>
                            <hr style="margin: 0.5rem 0; border: none; border-top: 1px solid var(--border-color);">
                            <form method="POST" action="{{ route('client.logout') }}" style="margin: 0;">
                                @csrf
                                <button type="submit" style="width: 100%; text-align: left; background: none; border: none; padding: 0.75rem 1rem; color: #ef4444; cursor: pointer; transition: background-color 0.2s ease; display: flex; align-items: center;">
                                    <i class="fas fa-sign-out-alt" style="margin-right: 0.5rem;"></i>
                                    Logout
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main id="mainContent" class="main-content">
            {{ $slot }}
        </main>
    </div>

    <script>
        // Sidebar functionality
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');
            const mainContent = document.getElementById('mainContent');
            
            sidebar.classList.toggle('open');
            overlay.classList.toggle('show');
            
            // On desktop, adjust main content margin
            if (window.innerWidth > 768) {
                if (sidebar.classList.contains('open')) {
                    mainContent.classList.add('content-with-sidebar');
                } else {
                    mainContent.classList.remove('content-with-sidebar');
                }
            }
        }

        // Dropdown functionality
        function toggleDropdown() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.classList.toggle('show');
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('userDropdown');
            const dropdownButton = event.target.closest('.dropdown button');
            
            if (!dropdownButton && dropdown.classList.contains('show')) {
                dropdown.classList.remove('show');
            }
        });

        // Dark mode functionality
        function toggleDarkMode() {
            const html = document.documentElement;
            const toggle = document.getElementById('darkModeToggle');
            const icon = toggle.querySelector('i');
            
            if (html.getAttribute('data-theme') === 'dark') {
                html.removeAttribute('data-theme');
                icon.className = 'fas fa-moon';
                localStorage.setItem('theme', 'light');
            } else {
                html.setAttribute('data-theme', 'dark');
                icon.className = 'fas fa-sun';
                localStorage.setItem('theme', 'dark');
            }
        }

        // Initialize theme on page load
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme');
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            const toggle = document.getElementById('darkModeToggle');
            const icon = toggle.querySelector('i');
            
            if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
                document.documentElement.setAttribute('data-theme', 'dark');
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            // Auto-open sidebar on desktop
            if (window.innerWidth > 768) {
                toggleSidebar();
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');
            const mainContent = document.getElementById('mainContent');
            
            if (window.innerWidth <= 768) {
                mainContent.classList.remove('content-with-sidebar');
            } else if (sidebar.classList.contains('open')) {
                mainContent.classList.add('content-with-sidebar');
                overlay.classList.remove('show');
            }
        });
    </script>
</body>

</html>
