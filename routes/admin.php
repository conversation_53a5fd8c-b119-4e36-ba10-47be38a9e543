<?php

use App\Http\Controllers\AdminAuthController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\Admin\ClientBalanceController;
use Illuminate\Support\Facades\Route;

// Guest admin routes (not authenticated)
Route::middleware('guest')->group(function () {
    Route::get('/login', [AdminAuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AdminAuthController::class, 'login'])->name('login.post');
});

// Authenticated admin routes
Route::middleware('auth')->group(function () {
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
    
    // Client management
    Route::get('/clients', [AdminController::class, 'clients'])->name('clients.index');
    Route::get('/clients/{client}', [AdminController::class, 'showClient'])->name('clients.show');
    Route::patch('/clients/{client}/status', [AdminController::class, 'updateClientStatus'])->name('clients.status');
    
    // SMS management
    Route::get('/sms', [AdminController::class, 'sms'])->name('sms.index');
    
    // Balance management
    Route::get('/client-balance', [ClientBalanceController::class, 'index'])->name('client-balance.index');
    Route::get('/client-balance/{client}', [ClientBalanceController::class, 'show'])->name('client-balance.show');
    Route::post('/client-balance/{client}/add', [ClientBalanceController::class, 'addBalance'])->name('client-balance.add');
    Route::post('/client-balance/{client}/deduct', [ClientBalanceController::class, 'deductBalance'])->name('client-balance.deduct');
    Route::post('/client-balance/bulk-add', [ClientBalanceController::class, 'bulkAddBalance'])->name('client-balance.bulk-add');
    Route::get('/balance-transactions', [ClientBalanceController::class, 'transactions'])->name('balance-transactions.index');
    
    // Profile management
    Route::get('/profile', [AdminController::class, 'profile'])->name('profile');
    Route::patch('/profile', [AdminController::class, 'updateProfile'])->name('profile.update');
    Route::patch('/profile/password', [AdminController::class, 'updatePassword'])->name('profile.password.update');
    
    // Settings management
    Route::get('/settings', [AdminController::class, 'settings'])->name('settings');
    Route::patch('/settings', [AdminController::class, 'updateSettings'])->name('settings.update');
    
    // Logout
    Route::post('/logout', [AdminAuthController::class, 'logout'])->name('logout');
});
