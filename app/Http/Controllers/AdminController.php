<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\Client;
use App\Models\SMS;
use App\Models\User;
use Carbon\Carbon;

class AdminController extends Controller
{
    /**
     * Show the admin dashboard.
     */
    public function dashboard()
    {
        // Get statistics for dashboard
        $totalClients = Client::count();
        $activeClients = Client::where('status', '2')->count();
        $totalSMS = SMS::count();
        $todaySMS = SMS::whereDate('created_at', Carbon::today())->count();
        
        // Get recent SMS messages
        $recentSMS = SMS::with('client')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();
        
        // Get recent clients
        $recentClients = Client::orderBy('created_at', 'desc')
            ->limit(5)
            ->get();
        
        // SMS status distribution
        $smsStats = [
            'sent' => SMS::where('status', 'sent')->count(),
            'pending' => SMS::where('status', 'pending')->count(),
            'failed' => SMS::where('status', 'failed')->count(),
        ];
        
        return view('admin.dashboard', compact(
            'totalClients',
            'activeClients', 
            'totalSMS',
            'todaySMS',
            'recentSMS',
            'recentClients',
            'smsStats'
        ));
    }

    /**
     * Show clients management page.
     */
    public function clients(Request $request)
    {
        $query = Client::query();
        
        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }
        
        // Status filter
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        $clients = $query->orderBy('created_at', 'desc')->paginate(15);
        
        return view('admin.clients.index', compact('clients'));
    }

    /**
     * Show client details.
     */
    public function showClient(Client $client)
    {
        $client->load(['sms' => function($query) {
            $query->orderBy('created_at', 'desc')->limit(20);
        }]);
        
        $smsStats = [
            'total' => $client->sms()->count(),
            'sent' => $client->sms()->where('status', 'sent')->count(),
            'pending' => $client->sms()->where('status', 'pending')->count(),
            'failed' => $client->sms()->where('status', 'failed')->count(),
        ];
        
        return view('admin.clients.show', compact('client', 'smsStats'));
    }

    /**
     * Update client status.
     */
    public function updateClientStatus(Request $request, Client $client)
    {
        $request->validate([
            'status' => 'required|in:1,2,3'
        ]);
        
        $client->update(['status' => $request->status]);
        
        $statusText = [
            '1' => 'pending',
            '2' => 'active', 
            '3' => 'deactivated'
        ];
        
        return back()->with('success', "Client status updated to {$statusText[$request->status]}.");
    }

    /**
     * Show SMS management page.
     */
    public function sms(Request $request)
    {
        $query = SMS::with('client');
        
        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('msg', 'like', "%{$search}%")
                  ->orWhere('to_number', 'like', "%{$search}%")
                  ->orWhere('from_number', 'like', "%{$search}%")
                  ->orWhereHas('client', function($clientQuery) use ($search) {
                      $clientQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }
        
        // Status filter
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // Date filter
        if ($request->has('date') && $request->date) {
            $query->whereDate('created_at', $request->date);
        }
        
        $smsMessages = $query->orderBy('created_at', 'desc')->paginate(20);
        
        return view('admin.sms.index', compact('smsMessages'));
    }

    /**
     * Show admin profile page.
     */
    public function profile()
    {
        $user = Auth::user();
        return view('admin.profile', compact('user'));
    }

    /**
     * Update admin profile.
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();
        
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'current_password' => 'nullable|required_with:password',
            'password' => 'nullable|min:6|confirmed',
        ]);
        
        // Update basic info
        $user->update([
            'name' => $request->name,
            'email' => $request->email,
        ]);
        
        // Update password if provided
        if ($request->password) {
            if (!Hash::check($request->current_password, $user->password)) {
                return back()->withErrors(['current_password' => 'Current password is incorrect.']);
            }
            
            $user->update(['password' => Hash::make($request->password)]);
        }
        
        return back()->with('success', 'Profile updated successfully.');
    }
}
