<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('balance_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->enum('type', ['credit', 'debit'])->comment('credit=add money, debit=deduct money');
            $table->decimal('amount', 10, 2);
            $table->decimal('balance_before', 10, 2);
            $table->decimal('balance_after', 10, 2);
            $table->string('description')->nullable();
            $table->string('reference')->nullable(); // SMS ID, Admin action, etc.
            $table->enum('source', ['admin', 'sms', 'payment', 'adjustment'])->default('admin');
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null'); // Admin who created
            $table->timestamps();
            
            $table->index(['client_id', 'created_at']);
            $table->index(['type', 'source']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('balance_transactions');
    }
};
